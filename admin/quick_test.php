<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

$userId = 27;
$courseId = 2;

echo "<h2>Quick Test</h2>";
echo "<p>Testing User ID: $userId, Course ID: $courseId</p>";

// Test user
$userStmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$userStmt->bind_param("i", $userId);
$userStmt->execute();
$user = $userStmt->get_result()->fetch_assoc();

if ($user) {
    echo "<p>✅ User found: " . htmlspecialchars($user['name']) . "</p>";
} else {
    echo "<p>❌ User NOT found</p>";
}

// Test course
$courseStmt = $conn->prepare("SELECT * FROM courses WHERE id = ?");
$courseStmt->bind_param("i", $courseId);
$courseStmt->execute();
$course = $courseStmt->get_result()->fetch_assoc();

if ($course) {
    echo "<p>✅ Course found: " . htmlspecialchars($course['title']) . "</p>";
} else {
    echo "<p>❌ Course NOT found</p>";
}

// Test if both exist
if ($user && $course) {
    echo "<p>✅ Both user and course exist - analytics should work</p>";
    echo "<p><a href='video_analytics.php?user_id=$userId&course_id=$courseId&debug=1'>Test Analytics Page</a></p>";
} else {
    echo "<p>❌ Missing user or course - analytics won't work</p>";
}
?>
