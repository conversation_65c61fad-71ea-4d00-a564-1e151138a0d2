<?php
/**
 * Real-time Video Progress Monitor
 * Shows live updates from Flutter app
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

$userId = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 27;
$courseId = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 2;
$autoRefresh = isset($_GET['auto_refresh']) ? (int)$_GET['auto_refresh'] : 5;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Video Progress Monitor</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <?php if ($autoRefresh > 0): ?>
    <meta http-equiv="refresh" content="<?php echo $autoRefresh; ?>">
    <?php endif; ?>
    <style>
        .monitor-card { 
            background: white; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            margin-bottom: 20px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active { background: #28a745; }
        .status-inactive { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .recent-activity {
            max-height: 400px;
            overflow-y: auto;
        }
        .activity-item {
            border-left: 3px solid #007bff;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .timestamp {
            font-size: 0.85em;
            color: #6c757d;
        }
    </style>
</head>
<body class="bg-light">

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line me-2"></i>Video Progress Monitor</h2>
                <div class="d-flex gap-2">
                    <span class="badge bg-primary">User: <?php echo $userId; ?></span>
                    <span class="badge bg-info">Course: <?php echo $courseId; ?></span>
                    <span class="badge bg-success">Auto-refresh: <?php echo $autoRefresh; ?>s</span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Real-time Stats -->
        <div class="col-md-4">
            <div class="monitor-card p-3">
                <h5><i class="fas fa-tachometer-alt me-2"></i>Live Statistics</h5>
                <?php
                try {
                    // Get latest activity
                    $latestStmt = $conn->prepare("
                        SELECT created_at 
                        FROM user_activity_log 
                        WHERE user_id = ? AND activity_type = 'video_progress'
                        ORDER BY created_at DESC 
                        LIMIT 1
                    ");
                    $latestStmt->bind_param("i", $userId);
                    $latestStmt->execute();
                    $latest = $latestStmt->get_result()->fetch_assoc();
                    
                    $lastActivity = $latest ? $latest['created_at'] : null;
                    $timeSinceLastActivity = $lastActivity ? time() - strtotime($lastActivity) : null;
                    
                    // Get total records today
                    $todayStmt = $conn->prepare("
                        SELECT COUNT(*) as count 
                        FROM user_activity_log 
                        WHERE user_id = ? 
                        AND activity_type = 'video_progress'
                        AND DATE(created_at) = CURDATE()
                    ");
                    $todayStmt->bind_param("i", $userId);
                    $todayStmt->execute();
                    $todayCount = $todayStmt->get_result()->fetch_assoc()['count'];
                    
                    // Get total records this hour
                    $hourStmt = $conn->prepare("
                        SELECT COUNT(*) as count 
                        FROM user_activity_log 
                        WHERE user_id = ? 
                        AND activity_type = 'video_progress'
                        AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                    ");
                    $hourStmt->bind_param("i", $userId);
                    $hourStmt->execute();
                    $hourCount = $hourStmt->get_result()->fetch_assoc()['count'];
                    
                    // Determine status
                    $status = 'inactive';
                    $statusText = 'No Activity';
                    if ($timeSinceLastActivity !== null) {
                        if ($timeSinceLastActivity < 60) {
                            $status = 'active';
                            $statusText = 'Active Now';
                        } elseif ($timeSinceLastActivity < 300) {
                            $status = 'warning';
                            $statusText = 'Recent Activity';
                        }
                    }
                ?>
                
                <div class="mb-3">
                    <span class="status-indicator status-<?php echo $status; ?>"></span>
                    <strong><?php echo $statusText; ?></strong>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-0"><?php echo $hourCount; ?></h4>
                            <small class="text-muted">This Hour</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-0"><?php echo $todayCount; ?></h4>
                        <small class="text-muted">Today</small>
                    </div>
                </div>
                
                <?php if ($lastActivity): ?>
                <div class="mt-3">
                    <small class="text-muted">
                        Last activity: <?php echo date('H:i:s', strtotime($lastActivity)); ?>
                        (<?php echo $timeSinceLastActivity; ?>s ago)
                    </small>
                </div>
                <?php endif; ?>
                
                <?php
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
                ?>
            </div>
            
            <!-- Controls -->
            <div class="monitor-card p-3">
                <h6><i class="fas fa-cog me-2"></i>Controls</h6>
                <div class="d-grid gap-2">
                    <a href="?user_id=<?php echo $userId; ?>&course_id=<?php echo $courseId; ?>&auto_refresh=0" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pause me-1"></i>Stop Auto-refresh
                    </a>
                    <a href="?user_id=<?php echo $userId; ?>&course_id=<?php echo $courseId; ?>&auto_refresh=5" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-sync me-1"></i>5s Refresh
                    </a>
                    <a href="video_analytics.php?user_id=<?php echo $userId; ?>&course_id=<?php echo $courseId; ?>" 
                       class="btn btn-primary btn-sm">
                        <i class="fas fa-chart-bar me-1"></i>View Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-md-8">
            <div class="monitor-card p-3">
                <h5><i class="fas fa-history me-2"></i>Recent Activity</h5>
                <div class="recent-activity">
                    <?php
                    try {
                        $activityStmt = $conn->prepare("
                            SELECT al.*, cv.title as video_title
                            FROM user_activity_log al
                            LEFT JOIN course_videos cv ON al.related_id = cv.id
                            WHERE al.user_id = ? 
                            AND al.activity_type = 'video_progress'
                            ORDER BY al.created_at DESC 
                            LIMIT 20
                        ");
                        $activityStmt->bind_param("i", $userId);
                        $activityStmt->execute();
                        $activities = $activityStmt->get_result();
                        
                        if ($activities->num_rows > 0) {
                            while ($activity = $activities->fetch_assoc()) {
                                $details = json_decode($activity['details'], true);
                                $action = $details['action'] ?? 'unknown';
                                $watchDuration = $details['watch_duration'] ?? 0;
                                $isCompleted = $details['is_completed'] ?? false;
                                
                                $actionIcon = $isCompleted ? 'fa-check-circle text-success' : 'fa-play-circle text-primary';
                                $actionText = $isCompleted ? 'Completed' : 'Progress Update';
                                
                                echo "<div class='activity-item'>";
                                echo "<div class='d-flex justify-content-between align-items-start'>";
                                echo "<div>";
                                echo "<i class='fas $actionIcon me-2'></i>";
                                echo "<strong>$actionText</strong>";
                                echo "<br><small class='text-muted'>";
                                echo "Video: " . htmlspecialchars($activity['video_title'] ?? 'Unknown');
                                echo " • Duration: {$watchDuration}s";
                                echo "</small>";
                                echo "</div>";
                                echo "<div class='timestamp'>";
                                echo date('H:i:s', strtotime($activity['created_at']));
                                echo "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='text-center text-muted py-4'>";
                            echo "<i class='fas fa-inbox fa-3x mb-3'></i>";
                            echo "<p>No recent activity found</p>";
                            echo "<small>Start watching videos in the Flutter app to see updates here</small>";
                            echo "</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error loading activity: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
