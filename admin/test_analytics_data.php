<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

echo "<h2>🔍 Analytics Data Test</h2>";

try {
    // Test 1: Check table existence
    echo "<h3>1. Table Existence Check</h3>";
    $tables = ['users', 'courses', 'course_videos', 'user_video_progress', 'user_activity_log', 'user_course_enrollments'];
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        $exists = $result->num_rows > 0;
        echo "<p><strong>$table:</strong> " . ($exists ? "✅ Exists" : "❌ Missing") . "</p>";
        
        if ($exists) {
            $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $countResult->fetch_assoc()['count'];
            echo "<p>&nbsp;&nbsp;&nbsp;Records: $count</p>";
        }
    }

    // Test 2: Check specific user and course data
    echo "<h3>2. Sample Data Check</h3>";
    
    // Users
    $result = $conn->query("SELECT id, name FROM users LIMIT 5");
    echo "<p><strong>Sample Users:</strong></p><ul>";
    while ($row = $result->fetch_assoc()) {
        echo "<li>ID: {$row['id']}, Name: {$row['name']}</li>";
    }
    echo "</ul>";
    
    // Courses
    $result = $conn->query("SELECT id, title FROM courses LIMIT 5");
    echo "<p><strong>Sample Courses:</strong></p><ul>";
    while ($row = $result->fetch_assoc()) {
        echo "<li>ID: {$row['id']}, Title: {$row['title']}</li>";
    }
    echo "</ul>";
    
    // Videos
    $result = $conn->query("SELECT id, title, course_id FROM course_videos LIMIT 5");
    echo "<p><strong>Sample Videos:</strong></p><ul>";
    while ($row = $result->fetch_assoc()) {
        echo "<li>ID: {$row['id']}, Title: {$row['title']}, Course: {$row['course_id']}</li>";
    }
    echo "</ul>";

    // Test 3: Check analytics data for user 1, course 1
    echo "<h3>3. Analytics Data for User 1, Course 1</h3>";
    
    $userId = 1;
    $courseId = 1;
    
    // Progress data
    $progressQuery = "SELECT v.id, v.title, p.watch_duration_seconds, p.is_completed, p.last_position_seconds
                      FROM course_videos v 
                      LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
                      WHERE v.course_id = ?";
    $stmt = $conn->prepare($progressQuery);
    $stmt->bind_param("ii", $userId, $courseId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<p><strong>Video Progress:</strong></p><ul>";
    while ($row = $result->fetch_assoc()) {
        $watchTime = $row['watch_duration_seconds'] ?? 0;
        $completed = $row['is_completed'] ? 'Yes' : 'No';
        echo "<li>{$row['title']}: {$watchTime}s watched, Completed: $completed</li>";
    }
    echo "</ul>";
    
    // Activity data
    $activityQuery = "SELECT al.*, cv.title as video_title 
                      FROM user_activity_log al
                      JOIN course_videos cv ON al.related_id = cv.id
                      WHERE al.user_id = ? AND cv.course_id = ? AND al.activity_type = 'video_progress'
                      ORDER BY al.created_at DESC LIMIT 5";
    $stmt = $conn->prepare($activityQuery);
    $stmt->bind_param("ii", $userId, $courseId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<p><strong>Recent Activity:</strong></p><ul>";
    while ($row = $result->fetch_assoc()) {
        $details = json_decode($row['details'], true);
        $action = $details['action'] ?? 'unknown';
        $watchDuration = $details['watch_duration'] ?? 0;
        echo "<li>{$row['video_title']}: $action ({$watchDuration}s) - {$row['created_at']}</li>";
    }
    echo "</ul>";

    // Test 4: Analytics summary
    echo "<h3>4. Analytics Summary for User 1, Course 1</h3>";
    
    $analyticsQuery = "SELECT
                      COUNT(DISTINCT p.video_id) as total_views,
                      SUM(p.watch_duration_seconds) as total_watch_duration,
                      COUNT(DISTINCT CASE WHEN p.is_completed = 1 THEN p.video_id END) as completed_videos,
                      MAX(p.updated_at) as last_activity
                      FROM user_video_progress p
                      JOIN course_videos cv ON p.video_id = cv.id
                      WHERE p.user_id = ? AND cv.course_id = ? AND p.watch_duration_seconds > 0";
    $stmt = $conn->prepare($analyticsQuery);
    $stmt->bind_param("ii", $userId, $courseId);
    $stmt->execute();
    $result = $stmt->get_result();
    $analytics = $result->fetch_assoc();
    
    echo "<ul>";
    echo "<li><strong>Total Views:</strong> {$analytics['total_views']}</li>";
    echo "<li><strong>Total Watch Time:</strong> {$analytics['total_watch_duration']} seconds</li>";
    echo "<li><strong>Completed Videos:</strong> {$analytics['completed_videos']}</li>";
    echo "<li><strong>Last Activity:</strong> {$analytics['last_activity']}</li>";
    echo "</ul>";

    echo "<h3>✅ Test Completed Successfully!</h3>";
    echo "<p><a href='video_analytics.php?user_id=1&course_id=1' class='btn btn-primary'>View Analytics Page</a></p>";

} catch (Exception $e) {
    echo "<h3>❌ Error occurred:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
