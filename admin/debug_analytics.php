<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Video Analytics Debug</h2>\n";
echo "<pre>\n";

try {
    echo "1. Testing basic PHP syntax...\n";
    echo "   ✅ PHP is working\n";

    echo "\n2. Testing includes...\n";
    
    // Test each include individually
    if (file_exists('includes/config.php')) {
        echo "   ✅ config.php exists\n";
        require_once 'includes/config.php';
        echo "   ✅ config.php loaded\n";
    } else {
        echo "   ❌ config.php missing\n";
    }

    if (file_exists('includes/auth.php')) {
        echo "   ✅ auth.php exists\n";
        require_once 'includes/auth.php';
        echo "   ✅ auth.php loaded\n";
    } else {
        echo "   ❌ auth.php missing\n";
    }

    if (file_exists('includes/database.php')) {
        echo "   ✅ database.php exists\n";
        require_once 'includes/database.php';
        echo "   ✅ database.php loaded\n";
    } else {
        echo "   ❌ database.php missing\n";
    }

    if (file_exists('includes/utilities.php')) {
        echo "   ✅ utilities.php exists\n";
        require_once 'includes/utilities.php';
        echo "   ✅ utilities.php loaded\n";
    } else {
        echo "   ❌ utilities.php missing\n";
    }

    echo "\n3. Testing database connection...\n";
    $db = new Database();
    $conn = $db->getConnection();
    echo "   ✅ Database connection successful\n";

    echo "\n4. Testing basic queries...\n";
    
    // Test users table
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    $count = $result->fetch_assoc()['count'];
    echo "   ✅ Users table: $count records\n";

    // Test courses table
    $result = $conn->query("SELECT COUNT(*) as count FROM courses");
    $count = $result->fetch_assoc()['count'];
    echo "   ✅ Courses table: $count records\n";

    // Test specific user and course
    $userId = 27;
    $courseId = 2;
    
    $userStmt = $conn->prepare("SELECT name FROM users WHERE id = ?");
    $userStmt->bind_param("i", $userId);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    if ($user = $userResult->fetch_assoc()) {
        echo "   ✅ User 27 found: {$user['name']}\n";
    } else {
        echo "   ❌ User 27 not found\n";
    }

    $courseStmt = $conn->prepare("SELECT title FROM courses WHERE id = ?");
    $courseStmt->bind_param("i", $courseId);
    $courseStmt->execute();
    $courseResult = $courseStmt->get_result();
    if ($course = $courseResult->fetch_assoc()) {
        echo "   ✅ Course 2 found: {$course['title']}\n";
    } else {
        echo "   ❌ Course 2 not found\n";
    }

    echo "\n5. Testing auth system...\n";
    $auth = new Auth();
    echo "   ✅ Auth object created\n";

    echo "\n6. Testing video analytics query...\n";
    $videosQuery = "SELECT v.*, 1 as is_unlocked, 0 as is_completed
                   FROM course_videos v
                   WHERE v.course_id = ?
                   ORDER BY v.week_number, v.sequence_number LIMIT 5";
    $videosStmt = $conn->prepare($videosQuery);
    $videosStmt->bind_param("i", $courseId);
    $videosStmt->execute();
    $videosResult = $videosStmt->get_result();
    $videoCount = $videosResult->num_rows;
    echo "   ✅ Found $videoCount videos for course $courseId\n";

    echo "\n✅ All basic tests passed!\n";
    echo "\nThe issue might be in the video_analytics.php file itself.\n";
    echo "Let's check the syntax...\n";

} catch (Exception $e) {
    echo "\n❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "\n❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>

<style>
body { font-family: monospace; background: #f5f5f5; padding: 20px; }
pre { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
h2 { color: #333; }
</style>
