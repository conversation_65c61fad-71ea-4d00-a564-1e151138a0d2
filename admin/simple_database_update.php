<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

echo "🔧 Updating database for video analytics...\n\n";

try {
    // 1. Update user_video_progress table structure
    echo "1. Updating user_video_progress table...\n";
    
    $columns = [
        'progress_percentage' => 'DECIMAL(5,2) DEFAULT 0.00',
        'watch_time_seconds' => 'INT DEFAULT 0',
        'completed' => 'BOOLEAN DEFAULT FALSE',
        'watch_duration_seconds' => 'INT DEFAULT 0',
        'last_position_seconds' => 'INT DEFAULT 0',
        'is_completed' => 'BOOLEAN DEFAULT FALSE',
        'is_unlocked' => 'BOOLEAN DEFAULT TRUE',
        'unlock_date' => 'DATE NULL',
        'completion_date' => 'DATETIME NULL',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($columns as $column => $definition) {
        $checkColumn = $conn->query("SHOW COLUMNS FROM user_video_progress LIKE '$column'");
        if ($checkColumn->num_rows == 0) {
            $sql = "ALTER TABLE user_video_progress ADD COLUMN $column $definition";
            $conn->query($sql);
            echo "   Added column: $column\n";
        }
    }
    
    // 2. Create user_activity_log table
    echo "\n2. Creating user_activity_log table...\n";
    $activityTableSql = "
    CREATE TABLE IF NOT EXISTS user_activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        activity_type VARCHAR(50) NOT NULL,
        related_id INT NULL,
        details JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_activity_type (activity_type),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->query($activityTableSql);
    echo "   user_activity_log table ready\n";
    
    // 3. Create user_course_enrollments table
    echo "\n3. Creating user_course_enrollments table...\n";
    $enrollmentTableSql = "
    CREATE TABLE IF NOT EXISTS user_course_enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        course_id INT NOT NULL,
        status VARCHAR(20) DEFAULT 'active',
        start_date DATE NOT NULL DEFAULT (CURRENT_DATE),
        end_date DATE NOT NULL DEFAULT (DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_course (user_id, course_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->query($enrollmentTableSql);
    echo "   user_course_enrollments table ready\n";
    
    // 4. Insert sample users
    echo "\n4. Creating sample users...\n";
    $sampleUsers = [
        [1, 'John Doe', 'john.doe', '<EMAIL>'],
        [2, 'Jane Smith', 'jane.smith', '<EMAIL>'],
        [3, 'Mike Johnson', 'mike.johnson', '<EMAIL>'],
        [4, 'Sarah Wilson', 'sarah.wilson', '<EMAIL>'],
        [5, 'David Brown', 'david.brown', '<EMAIL>']
    ];
    
    foreach ($sampleUsers as $user) {
        $checkUser = $conn->query("SELECT id FROM users WHERE id = {$user[0]}");
        if ($checkUser->num_rows == 0) {
            $stmt = $conn->prepare("INSERT INTO users (id, name, username, email, password, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
            $stmt->bind_param("issss", $user[0], $user[1], $user[2], $user[3], $hashedPassword);
            $stmt->execute();
            echo "   Created user: {$user[1]}\n";
        }
    }
    
    // 5. Insert sample enrollments
    echo "\n5. Creating sample enrollments...\n";
    for ($userId = 1; $userId <= 5; $userId++) {
        for ($courseId = 1; $courseId <= 3; $courseId++) {
            $checkEnrollment = $conn->query("SELECT id FROM user_course_enrollments WHERE user_id = $userId AND course_id = $courseId");
            if ($checkEnrollment->num_rows == 0) {
                $stmt = $conn->prepare("INSERT IGNORE INTO user_course_enrollments (user_id, course_id) VALUES (?, ?)");
                $stmt->bind_param("ii", $userId, $courseId);
                $stmt->execute();
            }
        }
    }
    echo "   Enrollments created\n";
    
    // 6. Clear and insert video progress data
    echo "\n6. Creating video progress data...\n";
    $conn->query("DELETE FROM user_video_progress WHERE user_id <= 5");
    $conn->query("DELETE FROM user_activity_log WHERE user_id <= 5");
    
    $progressData = [
        [1, 1, 85.50, 342, 0, 342, 342, 0],
        [1, 2, 100.00, 480, 1, 480, 480, 1],
        [1, 3, 45.25, 181, 0, 181, 181, 0],
        [2, 1, 100.00, 400, 1, 400, 400, 1],
        [2, 2, 67.80, 271, 0, 271, 271, 0],
        [2, 3, 92.30, 369, 0, 369, 369, 0],
        [3, 1, 78.90, 315, 0, 315, 315, 0],
        [3, 2, 100.00, 450, 1, 450, 450, 1],
        [3, 3, 23.40, 94, 0, 94, 94, 0],
        [4, 1, 56.70, 227, 0, 227, 227, 0],
        [4, 2, 89.20, 357, 0, 357, 357, 0],
        [4, 3, 100.00, 420, 1, 420, 420, 1],
        [5, 1, 100.00, 390, 1, 390, 390, 1],
        [5, 2, 34.60, 138, 0, 138, 138, 0],
        [5, 3, 71.80, 287, 0, 287, 287, 0]
    ];
    
    foreach ($progressData as $data) {
        $completionDate = $data[7] ? date('Y-m-d H:i:s') : null;
        $stmt = $conn->prepare("INSERT INTO user_video_progress (user_id, video_id, progress_percentage, watch_time_seconds, completed, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked, unlock_date, completion_date, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, CURDATE(), ?, NOW(), NOW())");
        $stmt->bind_param("iidiiiiis", $data[0], $data[1], $data[2], $data[3], $data[4], $data[5], $data[6], $data[7], $completionDate);
        $stmt->execute();
    }
    echo "   Progress data created\n";
    
    // 7. Insert activity log data
    echo "\n7. Creating activity log data...\n";
    $activityData = [
        [1, 1, 'progress', 342, false],
        [1, 2, 'complete', 480, true],
        [1, 3, 'progress', 181, false],
        [2, 1, 'complete', 400, true],
        [2, 2, 'progress', 271, false],
        [2, 3, 'progress', 369, false],
        [3, 1, 'progress', 315, false],
        [3, 2, 'complete', 450, true],
        [3, 3, 'progress', 94, false],
        [4, 1, 'progress', 227, false],
        [4, 2, 'progress', 357, false],
        [4, 3, 'complete', 420, true],
        [5, 1, 'complete', 390, true],
        [5, 2, 'progress', 138, false],
        [5, 3, 'progress', 287, false]
    ];
    
    foreach ($activityData as $data) {
        $details = json_encode([
            'action' => $data[2],
            'watch_duration' => $data[3],
            'last_position' => $data[3],
            'is_completed' => $data[4],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $stmt = $conn->prepare("INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at) VALUES (?, 'video_progress', ?, ?, NOW())");
        $stmt->bind_param("iis", $data[0], $data[1], $details);
        $stmt->execute();
    }
    echo "   Activity data created\n";
    
    // 8. Verification
    echo "\n8. Verification...\n";
    $userCount = $conn->query("SELECT COUNT(*) as count FROM users WHERE id <= 5")->fetch_assoc()['count'];
    $progressCount = $conn->query("SELECT COUNT(*) as count FROM user_video_progress WHERE user_id <= 5")->fetch_assoc()['count'];
    $activityCount = $conn->query("SELECT COUNT(*) as count FROM user_activity_log WHERE user_id <= 5")->fetch_assoc()['count'];
    
    echo "   ✅ Users: $userCount\n";
    echo "   ✅ Progress records: $progressCount\n";
    echo "   ✅ Activity records: $activityCount\n";
    
    echo "\n🎉 Database update completed successfully!\n";
    echo "\nTest the analytics at: http://localhost:8080/video_analytics.php?user_id=1&course_id=1\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
