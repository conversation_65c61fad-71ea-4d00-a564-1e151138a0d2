<?php
/**
 * Final Analytics Test
 * Comprehensive test of the entire video analytics system
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

$testUserId = 27;
$testCourseId = 2;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Final Analytics Test</title>
    <meta charset="utf-8">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">

<div class="container py-4">
    <h1 class="mb-4"><i class="fas fa-check-circle me-2"></i>Final Analytics Test</h1>
    
    <?php
    $allTestsPassed = true;
    $testResults = [];
    
    try {
        // Test 1: Database Tables
        echo "<div class='card mb-3'>";
        echo "<div class='card-header'><h5>1. Database Tables Test</h5></div>";
        echo "<div class='card-body'>";
        
        $tables = ['user_activity_log', 'user_video_progress', 'course_videos', 'users', 'courses'];
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows > 0) {
                echo "<p><i class='fas fa-check text-success me-2'></i>Table <code>$table</code> exists</p>";
                $testResults[$table] = true;
            } else {
                echo "<p><i class='fas fa-times text-danger me-2'></i>Table <code>$table</code> missing</p>";
                $testResults[$table] = false;
                $allTestsPassed = false;
            }
        }
        echo "</div></div>";
        
        // Test 2: Test Data
        echo "<div class='card mb-3'>";
        echo "<div class='card-header'><h5>2. Test Data Verification</h5></div>";
        echo "<div class='card-body'>";
        
        // Check user
        $userStmt = $conn->prepare("SELECT name FROM users WHERE id = ?");
        $userStmt->bind_param("i", $testUserId);
        $userStmt->execute();
        $user = $userStmt->get_result()->fetch_assoc();
        
        if ($user) {
            echo "<p><i class='fas fa-check text-success me-2'></i>User $testUserId found: " . htmlspecialchars($user['name']) . "</p>";
        } else {
            echo "<p><i class='fas fa-times text-danger me-2'></i>User $testUserId not found</p>";
            $allTestsPassed = false;
        }
        
        // Check course
        $courseStmt = $conn->prepare("SELECT title FROM courses WHERE id = ?");
        $courseStmt->bind_param("i", $testCourseId);
        $courseStmt->execute();
        $course = $courseStmt->get_result()->fetch_assoc();
        
        if ($course) {
            echo "<p><i class='fas fa-check text-success me-2'></i>Course $testCourseId found: " . htmlspecialchars($course['title']) . "</p>";
        } else {
            echo "<p><i class='fas fa-times text-danger me-2'></i>Course $testCourseId not found</p>";
            $allTestsPassed = false;
        }
        
        // Check videos
        $videoStmt = $conn->prepare("SELECT COUNT(*) as count FROM course_videos WHERE course_id = ?");
        $videoStmt->bind_param("i", $testCourseId);
        $videoStmt->execute();
        $videoCount = $videoStmt->get_result()->fetch_assoc()['count'];
        
        if ($videoCount > 0) {
            echo "<p><i class='fas fa-check text-success me-2'></i>Found $videoCount videos in course</p>";
        } else {
            echo "<p><i class='fas fa-times text-danger me-2'></i>No videos found in course $testCourseId</p>";
            $allTestsPassed = false;
        }
        
        echo "</div></div>";
        
        // Test 3: Analytics Data
        echo "<div class='card mb-3'>";
        echo "<div class='card-header'><h5>3. Analytics Data Test</h5></div>";
        echo "<div class='card-body'>";
        
        // Check progress records
        $progressStmt = $conn->prepare("SELECT COUNT(*) as count FROM user_video_progress WHERE user_id = ?");
        $progressStmt->bind_param("i", $testUserId);
        $progressStmt->execute();
        $progressCount = $progressStmt->get_result()->fetch_assoc()['count'];
        
        if ($progressCount > 0) {
            echo "<p><i class='fas fa-check text-success me-2'></i>Found $progressCount progress records for user</p>";
        } else {
            echo "<p><i class='fas fa-exclamation-triangle text-warning me-2'></i>No progress records found for user $testUserId</p>";
        }
        
        // Check activity records
        $activityStmt = $conn->prepare("SELECT COUNT(*) as count FROM user_activity_log WHERE user_id = ? AND activity_type = 'video_progress'");
        $activityStmt->bind_param("i", $testUserId);
        $activityStmt->execute();
        $activityCount = $activityStmt->get_result()->fetch_assoc()['count'];
        
        if ($activityCount > 0) {
            echo "<p><i class='fas fa-check text-success me-2'></i>Found $activityCount activity records for user</p>";
        } else {
            echo "<p><i class='fas fa-exclamation-triangle text-warning me-2'></i>No activity records found for user $testUserId</p>";
        }
        
        echo "</div></div>";
        
        // Test 4: Analytics Page
        echo "<div class='card mb-3'>";
        echo "<div class='card-header'><h5>4. Analytics Page Test</h5></div>";
        echo "<div class='card-body'>";
        
        $analyticsUrl = "video_analytics.php?user_id=$testUserId&course_id=$testCourseId";
        echo "<p><i class='fas fa-link me-2'></i>Analytics URL: <a href='$analyticsUrl' target='_blank'>$analyticsUrl</a></p>";
        
        // Test analytics query
        try {
            $analyticsQuery = "SELECT
                             COUNT(DISTINCT p.video_id) as total_views,
                             SUM(p.watch_duration_seconds) as total_watch_duration,
                             COUNT(DISTINCT CASE WHEN p.is_completed = 1 THEN p.video_id END) as completed_videos
                             FROM user_video_progress p
                             JOIN course_videos cv ON p.video_id = cv.id
                             WHERE p.user_id = ? AND cv.course_id = ? AND p.watch_duration_seconds > 0";
            $analyticsStmt = $conn->prepare($analyticsQuery);
            $analyticsStmt->bind_param("ii", $testUserId, $testCourseId);
            $analyticsStmt->execute();
            $analytics = $analyticsStmt->get_result()->fetch_assoc();
            
            if ($analytics) {
                echo "<p><i class='fas fa-check text-success me-2'></i>Analytics query successful:</p>";
                echo "<ul>";
                echo "<li>Total Views: " . ($analytics['total_views'] ?? 0) . "</li>";
                echo "<li>Watch Duration: " . ($analytics['total_watch_duration'] ?? 0) . " seconds</li>";
                echo "<li>Completed Videos: " . ($analytics['completed_videos'] ?? 0) . "</li>";
                echo "</ul>";
                
                if ($analytics['total_views'] > 0) {
                    echo "<p><i class='fas fa-check text-success me-2'></i>Analytics showing data - SUCCESS!</p>";
                } else {
                    echo "<p><i class='fas fa-exclamation-triangle text-warning me-2'></i>Analytics query works but no data found</p>";
                }
            } else {
                echo "<p><i class='fas fa-times text-danger me-2'></i>Analytics query failed</p>";
                $allTestsPassed = false;
            }
        } catch (Exception $e) {
            echo "<p><i class='fas fa-times text-danger me-2'></i>Analytics query error: " . htmlspecialchars($e->getMessage()) . "</p>";
            $allTestsPassed = false;
        }
        
        echo "</div></div>";
        
        // Test 5: API Endpoint
        echo "<div class='card mb-3'>";
        echo "<div class='card-header'><h5>5. API Endpoint Test</h5></div>";
        echo "<div class='card-body'>";
        
        if (file_exists('../api/video_progress.php')) {
            echo "<p><i class='fas fa-check text-success me-2'></i>API endpoint file exists</p>";
            echo "<p><i class='fas fa-info-circle text-info me-2'></i>API URL: <code>/api/video_progress.php</code></p>";
            echo "<p><i class='fas fa-info-circle text-info me-2'></i>Method: POST with JWT authentication</p>";
        } else {
            echo "<p><i class='fas fa-times text-danger me-2'></i>API endpoint file missing</p>";
            $allTestsPassed = false;
        }
        
        echo "</div></div>";
        
        // Final Result
        echo "<div class='card'>";
        if ($allTestsPassed) {
            echo "<div class='card-header bg-success text-white'>";
            echo "<h4><i class='fas fa-check-circle me-2'></i>All Tests Passed!</h4>";
            echo "</div>";
            echo "<div class='card-body'>";
            echo "<p class='mb-3'>✅ The video analytics system is <strong>WORKING</strong> and ready for use.</p>";
            echo "<div class='d-grid gap-2 d-md-flex justify-content-md-start'>";
            echo "<a href='$analyticsUrl' class='btn btn-primary'><i class='fas fa-chart-bar me-2'></i>View Analytics</a>";
            echo "<a href='monitor_video_progress.php?user_id=$testUserId&course_id=$testCourseId' class='btn btn-info'><i class='fas fa-eye me-2'></i>Monitor Progress</a>";
            echo "</div>";
        } else {
            echo "<div class='card-header bg-danger text-white'>";
            echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>Some Tests Failed</h4>";
            echo "</div>";
            echo "<div class='card-body'>";
            echo "<p class='mb-3'>❌ There are issues that need to be resolved before the analytics will work properly.</p>";
            echo "<p>Please review the failed tests above and fix the issues.</p>";
        }
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>Test Error</h5>";
        echo "<p>Error running tests: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="mt-4">
        <h5>Next Steps:</h5>
        <ol>
            <li><strong>Test Flutter App:</strong> Play videos in the Flutter app and monitor for API calls</li>
            <li><strong>Monitor Real-time:</strong> Use the progress monitor to see live updates</li>
            <li><strong>Verify Analytics:</strong> Check that the analytics page shows accurate data</li>
            <li><strong>Production Ready:</strong> Re-enable authentication and deploy</li>
        </ol>
    </div>
</div>

</body>
</html>
