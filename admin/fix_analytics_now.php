<?php
/**
 * Quick Fix for Video Analytics
 * Creates missing table and test data
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

echo "<!DOCTYPE html><html><head><title>Fix Analytics</title></head><body>";
echo "<h1>Fixing Video Analytics</h1>";

try {
    // 1. Create user_activity_log table
    echo "<h3>1. Creating user_activity_log table...</h3>";
    
    $createTableSql = "
    CREATE TABLE IF NOT EXISTS user_activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        activity_type VARCHAR(50) NOT NULL,
        related_id INT NULL,
        details JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_activity_type (activity_type),
        INDEX idx_user_type_related (user_id, activity_type, related_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($createTableSql)) {
        echo "<p>✅ user_activity_log table created successfully</p>";
    } else {
        echo "<p>❌ Error creating table: " . $conn->error . "</p>";
    }

    // 2. Add test data
    echo "<h3>2. Adding test data...</h3>";
    
    $testUserId = 27;
    $testCourseId = 2;
    
    // Get first video from course
    $videoStmt = $conn->prepare("SELECT id, title FROM course_videos WHERE course_id = ? LIMIT 1");
    $videoStmt->bind_param("i", $testCourseId);
    $videoStmt->execute();
    $video = $videoStmt->get_result()->fetch_assoc();
    
    if ($video) {
        echo "<p>Using video: " . htmlspecialchars($video['title']) . " (ID: {$video['id']})</p>";
        
        // Add progress record
        $progressSql = "
        INSERT INTO user_video_progress 
        (user_id, video_id, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked)
        VALUES (?, ?, 180, 180, 1, 1)
        ON DUPLICATE KEY UPDATE
        watch_duration_seconds = 180,
        last_position_seconds = 180,
        is_completed = 1,
        updated_at = NOW()
        ";
        
        $progressStmt = $conn->prepare($progressSql);
        $progressStmt->bind_param("ii", $testUserId, $video['id']);
        
        if ($progressStmt->execute()) {
            echo "<p>✅ Progress record added</p>";
        } else {
            echo "<p>❌ Error adding progress: " . $conn->error . "</p>";
        }
        
        // Add activity log
        $activitySql = "
        INSERT INTO user_activity_log (user_id, activity_type, related_id, details)
        VALUES (?, 'video_progress', ?, ?)
        ";
        
        $details = json_encode([
            'action' => 'complete',
            'watch_duration' => 180,
            'last_position' => 180,
            'is_completed' => true,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $activityStmt = $conn->prepare($activitySql);
        $activityStmt->bind_param("iis", $testUserId, $video['id'], $details);
        
        if ($activityStmt->execute()) {
            echo "<p>✅ Activity record added</p>";
        } else {
            echo "<p>❌ Error adding activity: " . $conn->error . "</p>";
        }
        
        // Add a few more test records
        for ($i = 1; $i <= 3; $i++) {
            $watchTime = 60 * $i;
            $testDetails = json_encode([
                'action' => 'progress_update',
                'watch_duration' => $watchTime,
                'last_position' => $watchTime,
                'is_completed' => false,
                'timestamp' => date('Y-m-d H:i:s', strtotime("-$i hours"))
            ]);
            
            $activityStmt = $conn->prepare($activitySql);
            $activityStmt->bind_param("iis", $testUserId, $video['id'], $testDetails);
            $activityStmt->execute();
        }
        
        echo "<p>✅ Additional test records added</p>";
        
    } else {
        echo "<p>❌ No videos found in course $testCourseId</p>";
    }

    // 3. Verify data
    echo "<h3>3. Verifying data...</h3>";
    
    $result = $conn->query("SELECT COUNT(*) as count FROM user_activity_log");
    $count = $result->fetch_assoc()['count'];
    echo "<p>Total activity records: $count</p>";
    
    $userStmt = $conn->prepare("SELECT COUNT(*) as count FROM user_activity_log WHERE user_id = ?");
    $userStmt->bind_param("i", $testUserId);
    $userStmt->execute();
    $userCount = $userStmt->get_result()->fetch_assoc()['count'];
    echo "<p>User $testUserId activity records: $userCount</p>";
    
    $progressStmt = $conn->prepare("SELECT COUNT(*) as count FROM user_video_progress WHERE user_id = ?");
    $progressStmt->bind_param("i", $testUserId);
    $progressStmt->execute();
    $progressCount = $progressStmt->get_result()->fetch_assoc()['count'];
    echo "<p>User $testUserId progress records: $progressCount</p>";

    echo "<h3>✅ All Done!</h3>";
    echo "<p><a href='video_analytics.php?user_id=$testUserId&course_id=$testCourseId' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Analytics Page</a></p>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
