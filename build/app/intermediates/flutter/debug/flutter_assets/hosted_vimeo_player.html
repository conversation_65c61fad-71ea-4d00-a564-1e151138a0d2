<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#000000">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    <title>KFT Video Player</title>
    <style>
        /* Reset and base styles for native browser feel */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        html, body {
            width: 100vw;
            height: 100vh;
            background: #000;
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            touch-action: manipulation;
            -ms-touch-action: manipulation;
        }

        #player-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #vimeo-player {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            border: none;
            background: #000;
            object-fit: cover;
        }

        /* Native loading indicator */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
            transition: opacity 0.3s ease-out;
        }

        .loading-spinner {
            width: 48px;
            height: 48px;
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-top: 3px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        .loading-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            font-weight: 500;
            text-align: center;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Error state */
        .error-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 20;
            padding: 20px;
        }

        .error-icon {
            width: 64px;
            height: 64px;
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .error-title {
            color: #ffffff;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            text-align: center;
        }

        .error-message {
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            text-align: center;
            line-height: 1.4;
            margin-bottom: 24px;
        }

        .retry-button {
            background: #007AFF;
            color: #ffffff;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .retry-button:hover {
            background: #0056CC;
        }

        .retry-button:active {
            background: #004499;
        }

        /* Hidden state */
        .hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* Responsive design for orientation changes */
        @media (orientation: landscape) {
            #vimeo-player {
                object-fit: cover;
            }
        }

        @media (orientation: portrait) {
            #vimeo-player {
                object-fit: contain;
            }
        }

        /* Prevent context menu and selection */
        body {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Loading overlay */
        #loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            transition: opacity 0.3s ease;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Error overlay */
        #error-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1001;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .error-content {
            max-width: 300px;
        }

        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.7;
        }

        .error-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .error-message {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.4;
        }

        /* Seek feedback overlay */
        #seek-feedback {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            z-index: 999;
            display: none;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .seek-success {
            background: rgba(34, 197, 94, 0.9);
            border-color: rgba(34, 197, 94, 0.3);
        }

        .seek-error {
            background: rgba(239, 68, 68, 0.9);
            border-color: rgba(239, 68, 68, 0.3);
        }

        /* Privacy error overlay */
        #privacy-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1002;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .privacy-content {
            max-width: 350px;
        }

        .privacy-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.7;
        }

        .privacy-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .privacy-message {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .retry-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .retry-button:hover {
            background: #2563eb;
        }

        /* Responsive adjustments */
        @media (max-width: 480px) {
            .error-content, .privacy-content {
                max-width: 280px;
            }

            .error-title, .privacy-title {
                font-size: 16px;
            }

            .error-message, .privacy-message {
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div id="player-container">
        <!-- Vimeo Player Iframe -->
        <iframe id="vimeo-player"
                src=""
                frameborder="0"
                allow="autoplay; fullscreen; picture-in-picture; encrypted-media; gyroscope; accelerometer"
                allowfullscreen
                sandbox="allow-scripts allow-same-origin allow-presentation allow-forms"
                referrerpolicy="strict-origin-when-cross-origin">
        </iframe>

        <!-- Loading Overlay -->
        <div id="loading-overlay">
            <div class="loading-spinner"></div>
        </div>

        <!-- Error Overlay -->
        <div id="error-overlay">
            <div class="error-content">
                <div class="error-icon">⚠️</div>
                <div class="error-title">Playback Error</div>
                <div class="error-message" id="error-message">
                    Unable to load video. Please try again.
                </div>
            </div>
        </div>

        <!-- Privacy Error Overlay -->
        <div id="privacy-overlay">
            <div class="privacy-content">
                <div class="privacy-icon">🔒</div>
                <div class="privacy-title">Video Privacy Settings</div>
                <div class="privacy-message">
                    This video has privacy restrictions. We're attempting to authenticate access...
                </div>
                <button class="retry-button" onclick="retryWithAuthentication()">
                    Retry with Authentication
                </button>
            </div>
        </div>

        <!-- Seek Feedback -->
        <div id="seek-feedback"></div>
    </div>

    <script>
        // Global variables
        let player = null;
        let isPlayerReady = false;
        let hasStartedPlaying = false;
        let currentVideoId = null;
        let seekThrottleTimer = null;
        let lastSeekTime = 0;
        let isSeekingInProgress = false;
        let retryCount = 0;
        let maxRetries = 3;

        // Configuration from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const vimeoId = urlParams.get('vimeo_id');
        const videoId = urlParams.get('video_id');
        const domain = urlParams.get('domain') || 'mycloudforge.com';
        const autoplay = urlParams.get('autoplay') === '1';
        const authToken = urlParams.get('auth_token');
        const secureHash = urlParams.get('h');

        // Enhanced domain verification with proper authentication
        const allowedDomains = ['mycloudforge.com', 'localhost', '127.0.0.1'];
        const currentHostname = window.location.hostname;
        const isDomainAllowed = allowedDomains.some(allowedDomain => {
            return currentHostname === allowedDomain ||
                   currentHostname.endsWith('.' + allowedDomain) ||
                   (allowedDomain === 'localhost' && (currentHostname === '127.0.0.1' || currentHostname === '::1'));
        });

        // Verify referrer for additional security
        const referrer = document.referrer;
        const isValidReferrer = !referrer || allowedDomains.some(domain => referrer.includes(domain));

        // Domain authentication token verification
        const isDomainAuthenticated = domain && allowedDomains.includes(domain);

        console.log('🔄 KFT Hosted Player initializing...', {
            vimeoId,
            videoId,
            domain,
            autoplay,
            isDomainAllowed,
            isValidReferrer,
            isDomainAuthenticated,
            hostname: currentHostname,
            referrer: referrer
        });

        // Initialize player when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Validate required parameters
            if (!vimeoId) {
                showError('Missing Video ID', 'No Vimeo video ID provided in URL parameters.');
                return;
            }

            // Enhanced domain verification
            if (!isDomainAllowed) {
                console.error('❌ Domain verification failed:', {
                    currentHostname,
                    allowedDomains,
                    isDomainAllowed
                });
                showError('Domain Not Authorized',
                    `This player can only be used from authorized domains. Current domain: ${currentHostname}`);
                return;
            }

            if (!isValidReferrer) {
                console.error('❌ Referrer verification failed:', { referrer });
                showError('Invalid Referrer', 'This player must be accessed from an authorized source.');
                return;
            }

            if (!isDomainAuthenticated) {
                console.error('❌ Domain authentication failed:', { domain });
                showError('Domain Authentication Required', 'Valid domain authentication is required.');
                return;
            }

            console.log('✅ Domain verification passed, initializing player...');
            initializePlayer();
        });

        function initializePlayer() {
            console.log('🎬 Initializing domain-protected Vimeo player for video:', vimeoId);

            // Build domain-verified Vimeo embed URL for mycloudforge.com
            let embedUrl = `https://player.vimeo.com/video/${vimeoId}`;

            // Start with secure hash if provided (for private videos)
            const urlParams = new URLSearchParams();
            if (secureHash) {
                urlParams.set('h', secureHash);
            }

            // CRITICAL: Set the exact domain that's whitelisted in Vimeo
            urlParams.set('referrer', 'https://mycloudforge.com/');
            urlParams.set('origin', 'https://mycloudforge.com');

            // Add authentication token if provided
            if (authToken) {
                urlParams.set('auth_token', authToken);
            }

            // Add player parameters optimized for domain-protected videos
            urlParams.set('autoplay', '0'); // Start with manual play for domain-protected videos
            urlParams.set('muted', '0');
            urlParams.set('title', '0');
            urlParams.set('byline', '0');
            urlParams.set('portrait', '0');
            urlParams.set('responsive', '1');
            urlParams.set('dnt', '1');
            urlParams.set('controls', '1');
            urlParams.set('sharing', '0');
            urlParams.set('download', '0');
            urlParams.set('fullscreen', '1');
            urlParams.set('autopause', '0');
            urlParams.set('background', '0');
            urlParams.set('playsinline', '1');
            urlParams.set('keyboard', '1');
            urlParams.set('pip', '0');
            urlParams.set('quality', 'auto');
            urlParams.set('transparent', '0');
            urlParams.set('color', 'ffffff');

            // Add domain verification for mycloudforge.com
            urlParams.set('app_id', '122963');
            urlParams.set('player_id', 'kft_domain_player');

            embedUrl += '?' + urlParams.toString();

            console.log('🔗 Loading domain-protected Vimeo embed URL:', embedUrl);
            console.log('🏠 Domain verification: mycloudforge.com (whitelisted)');

            // Set iframe source
            const iframe = document.getElementById('vimeo-player');
            iframe.src = embedUrl;

            // Initialize Vimeo Player API
            initializeVimeoAPI(iframe);

            // Set up enhanced error handling for domain-protected videos
            iframe.onerror = function() {
                console.error('❌ Iframe failed to load domain-protected video');
                handlePlayerError('Failed to load domain-protected video. Please check domain whitelist.');
            };

            iframe.onload = function() {
                console.log('✅ Domain-protected iframe loaded successfully');

                // Wait a bit for Vimeo to verify domain, then hide loading
                setTimeout(() => {
                    hideLoading();
                    console.log('🎬 Domain-protected video ready for playback');
                }, 2000);

                // Optimize for native browser experience
                optimizeForNativeFeel();
            };
        }

        function optimizeForNativeFeel() {
            console.log('🎯 Optimizing for native browser experience...');

            // Prevent zoom and scroll behaviors
            document.addEventListener('touchstart', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault(); // Prevent pinch zoom
                }
            }, { passive: false });

            document.addEventListener('touchmove', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault(); // Prevent pinch zoom
                }
            }, { passive: false });

            // Prevent context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
            });

            // Prevent text selection
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
            });

            // Handle orientation changes for responsive design
            window.addEventListener('orientationchange', function() {
                setTimeout(() => {
                    const iframe = document.getElementById('vimeo-player');
                    iframe.style.width = '100vw';
                    iframe.style.height = '100vh';
                    console.log('📱 Orientation changed, adjusted player dimensions');
                }, 100);
            });

            // Optimize viewport for mobile
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content',
                    'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover, maximum-scale=1.0');
            }

            // Hide loading after reasonable timeout
            setTimeout(() => {
                if (document.getElementById('loading-overlay').style.display !== 'none') {
                    console.log('⏰ Auto-hiding loading overlay after timeout');
                    hideLoading();
                }
            }, 8000);
        }

        function initializeVimeoAPI(iframe) {
            // Import Vimeo Player API for domain-protected videos
            const script = document.createElement('script');
            script.src = 'https://player.vimeo.com/api/player.js';
            script.onload = function() {
                console.log('📚 Vimeo API loaded for domain-protected video, creating player instance');

                // Wait for iframe to fully load before creating player
                setTimeout(() => {
                    try {
                        player = new Vimeo.Player(iframe, {
                            // Additional options for domain-protected videos
                            responsive: true,
                            autopause: false
                        });
                        setupPlayerEvents();
                        console.log('✅ Domain-protected Vimeo player created successfully');
                    } catch (error) {
                        console.error('❌ Failed to create domain-protected Vimeo player:', error);
                        console.log('🔄 Retrying without API...');
                        // If API fails, the iframe should still work for basic playback
                        hideLoading();
                    }
                }, 1000);
            };
            script.onerror = function() {
                console.error('❌ Failed to load Vimeo API for domain-protected video');
                console.log('🔄 Continuing with basic iframe playback...');
                // Domain-protected videos should still work without API
                hideLoading();
            };
            document.head.appendChild(script);
        }

        function setupPlayerEvents() {
            console.log('🎯 Setting up events for domain-protected player');

            player.ready().then(function() {
                console.log('✅ Domain-protected Vimeo player ready');
                isPlayerReady = true;
                hideLoading();

                // For domain-protected videos, don't attempt autoplay initially
                console.log('🎬 Domain-protected video ready for manual playback');

                notifyFlutter('onPlayerReady', { videoId: vimeoId });
            }).catch(function(error) {
                console.error('🚫 Domain-protected Vimeo player failed to load:', error);

                // Check if it's a domain protection error
                if (error.message && error.message.includes('domain')) {
                    console.error('🔒 Domain protection error - check Vimeo whitelist');
                    showError('Domain Protection Error',
                        'This video is domain-protected. Please ensure mycloudforge.com is whitelisted in Vimeo.');
                } else {
                    handleVimeoError(error);
                }
            });

            // Playback events
            player.on('play', function() {
                console.log('▶️ Video started playing');
                hasStartedPlaying = true;
                notifyFlutter('onPlay', {});
            });

            player.on('pause', function() {
                console.log('⏸️ Video paused');
                notifyFlutter('onPause', {});
            });

            player.on('ended', function() {
                console.log('🏁 Video ended');
                notifyFlutter('onCompleted', {});
            });

            // Progress tracking
            player.on('timeupdate', function(data) {
                const position = Math.floor(data.seconds);
                notifyFlutter('onProgress', { position });
            });

            // Error handling
            player.on('error', function(error) {
                console.error('🚫 Vimeo player error:', error);
                handleVimeoError(error);
            });

            // Seeking events
            player.on('seeked', function(data) {
                console.log('🎯 Seek completed to:', data.seconds);
                isSeekingInProgress = false;
                showSeekFeedback(`Seeked to ${formatTime(data.seconds)}`, true);
                notifyFlutter('onSeekSuccess', {
                    position: Math.floor(data.seconds),
                    targetPosition: Math.floor(data.seconds)
                });
            });
        }

        // Seek throttling implementation
        function throttledSeek(targetPosition) {
            const now = Date.now();
            const timeSinceLastSeek = now - lastSeekTime;

            console.log(`🎛️ THROTTLED SEEK: Request to ${targetPosition}s (${timeSinceLastSeek}ms since last)`);

            // Clear any pending seek
            if (seekThrottleTimer) {
                clearTimeout(seekThrottleTimer);
                console.log('🔄 THROTTLED SEEK: Cancelled pending seek');
            }

            // If enough time has passed, seek immediately
            if (timeSinceLastSeek >= 400) {
                performSeek(targetPosition);
                return true;
            }

            // Otherwise, throttle the seek
            console.log('⏳ THROTTLED SEEK: Throttling seek request');
            isSeekingInProgress = true;
            showSeekFeedback(`Seeking to ${formatTime(targetPosition)}...`, null, true);

            seekThrottleTimer = setTimeout(() => {
                performSeek(targetPosition);
            }, 400 - timeSinceLastSeek);

            return true;
        }

        function performSeek(targetPosition) {
            if (!player || !isPlayerReady) {
                console.warn('⚠️ SEEK: Player not ready');
                notifyFlutter('onSeekFailure', {
                    targetPosition,
                    error: 'Player not ready',
                    isSeekError: true
                });
                return false;
            }

            console.log(`🎯 SEEK: Executing seek to ${targetPosition}s`);
            lastSeekTime = Date.now();
            isSeekingInProgress = true;

            player.setCurrentTime(targetPosition).then(function(seconds) {
                console.log(`✅ SEEK: Successfully seeked to ${seconds}s`);
                isSeekingInProgress = false;
                showSeekFeedback(`Seeked to ${formatTime(seconds)}`, true);
                notifyFlutter('onSeekSuccess', {
                    position: Math.floor(seconds),
                    targetPosition: Math.floor(targetPosition)
                });
            }).catch(function(error) {
                console.error(`❌ SEEK: Failed to seek to ${targetPosition}s:`, error);
                isSeekingInProgress = false;

                const isPrivacyError = error.name === 'PrivacyError' ||
                                     error.message.includes('privacy') ||
                                     error.message.includes('Privacy');
                const isNetworkError = error.name === 'NetworkError' ||
                                     error.message.includes('network') ||
                                     error.message.includes('Network');

                if (isPrivacyError && hasStartedPlaying) {
                    showPrivacyOverlay();
                }

                showSeekFeedback(`Failed to seek to ${formatTime(targetPosition)}`, false);
                notifyFlutter('onSeekFailure', {
                    targetPosition,
                    error: error.message || 'Seek failed',
                    isPrivacyError,
                    isNetworkError,
                    isSeekError: true,
                    isThrottled: true
                });
            });

            return true;
        }

        // Player control functions
        function playVideo() {
            if (!player || !isPlayerReady) return false;

            player.play().then(function() {
                console.log('▶️ Play command successful');
                notifyFlutter('onPlay', {});
            }).catch(function(error) {
                console.error('❌ Play failed:', error);
                handleVimeoError(error);
            });

            return true;
        }

        function pauseVideo() {
            if (!player || !isPlayerReady) return false;

            player.pause().then(function() {
                console.log('⏸️ Pause command successful');
                notifyFlutter('onPause', {});
            }).catch(function(error) {
                console.error('❌ Pause failed:', error);
                handleVimeoError(error);
            });

            return true;
        }

        function getCurrentTime() {
            if (!player || !isPlayerReady) return 0;

            return player.getCurrentTime().then(function(seconds) {
                return Math.floor(seconds);
            }).catch(function(error) {
                console.error('❌ Get current time failed:', error);
                return 0;
            });
        }

        function getDuration() {
            if (!player || !isPlayerReady) return 0;

            return player.getDuration().then(function(duration) {
                return Math.floor(duration);
            }).catch(function(error) {
                console.error('❌ Get duration failed:', error);
                return 0;
            });
        }

        // Error handling functions
        function handleVimeoError(error) {
            console.error('🚫 Handling Vimeo error:', error);

            const isPrivacyError = error.name === 'PrivacyError' ||
                                 error.message.includes('privacy') ||
                                 error.message.includes('Privacy');

            if (isPrivacyError) {
                showPrivacyOverlay();
                notifyFlutter('onPrivacyError', {
                    error: error.message || 'Privacy settings prevent playback'
                });
            } else {
                handlePlayerError(error.message || 'Video playback error');
                notifyFlutter('onError', {
                    error: error.message || 'Video playback error'
                });
            }
        }

        function handlePlayerError(message) {
            console.error('❌ Player error:', message);
            hideLoading();
            showError('Playback Error', message);
        }

        function retryWithAuthentication() {
            if (retryCount >= maxRetries) {
                showError('Authentication Failed', 'Unable to authenticate after multiple attempts.');
                return;
            }

            retryCount++;
            hidePrivacyOverlay();
            showLoading();

            console.log(`🔄 Retrying with authentication (attempt ${retryCount}/${maxRetries})`);

            // Notify Flutter to attempt authentication
            notifyFlutter('onAuthenticationRequired', {
                vimeoId,
                videoId,
                retryCount
            });

            // Retry after a delay
            setTimeout(() => {
                initializePlayer();
            }, 2000);
        }

        // UI helper functions
        function showLoading() {
            document.getElementById('loading-overlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loading-overlay').style.display = 'none';
        }

        function showError(title, message) {
            document.getElementById('error-message').textContent = message;
            document.getElementById('error-overlay').style.display = 'flex';
        }

        function hideError() {
            document.getElementById('error-overlay').style.display = 'none';
        }

        function showPrivacyOverlay() {
            document.getElementById('privacy-overlay').style.display = 'flex';
        }

        function hidePrivacyOverlay() {
            document.getElementById('privacy-overlay').style.display = 'none';
        }

        function showSeekFeedback(message, isSuccess = null, showProgress = false) {
            const feedback = document.getElementById('seek-feedback');
            feedback.textContent = message;
            feedback.className = '';

            if (isSuccess === true) {
                feedback.classList.add('seek-success');
            } else if (isSuccess === false) {
                feedback.classList.add('seek-error');
            }

            feedback.style.display = 'block';

            // Auto-hide after 2 seconds
            setTimeout(() => {
                feedback.style.display = 'none';
            }, 2000);
        }

        // Utility functions
        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        function notifyFlutter(event, data) {
            // Send message to Flutter WebView
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler(event, JSON.stringify(data));
            } else if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[event]) {
                window.webkit.messageHandlers[event].postMessage(JSON.stringify(data));
            } else {
                console.log(`📱 Flutter message: ${event}`, data);
            }
        }

        // Expose functions to Flutter
        window.throttledSeek = throttledSeek;
        window.playVideo = playVideo;
        window.pauseVideo = pauseVideo;
        window.getCurrentTime = getCurrentTime;
        window.getDuration = getDuration;
        window.retryWithAuthentication = retryWithAuthentication;

        console.log('🎬 KFT Hosted Player script loaded and ready');
    </script>
</body>
</html>
