<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Temporarily disable auth check for debugging
/*
// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
    exit;
}

// Check if user has admin role
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to access this page.');
    Utilities::redirect('index.php');
    exit;
}
*/

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get course ID from query parameters
$courseId = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

// Debug: Log the received parameters
error_log("Video Analytics - Received parameters: User ID = $userId, Course ID = $courseId");

// Get all courses for dropdown
$coursesQuery = "SELECT id, title FROM courses ORDER BY title";
$coursesStmt = $conn->prepare($coursesQuery);
$coursesStmt->execute();
$coursesResult = $coursesStmt->get_result();
$courses = [];
while ($course = $coursesResult->fetch_assoc()) {
    $courses[] = $course;
}

// Get all users for dropdown
$usersQuery = "SELECT id, name, username FROM users ORDER BY name";
$usersStmt = $conn->prepare($usersQuery);
$usersStmt->execute();
$usersResult = $usersStmt->get_result();
$users = [];
while ($user = $usersResult->fetch_assoc()) {
    $users[] = $user;
}

// Get analytics data if course ID and user ID are provided
$analytics = [];
$videos = [];
$selectedCourse = null;
$selectedUser = null;
$debugInfo = [];

if ($courseId > 0 && $userId > 0) {
    try {
        // Get course details
        $courseQuery = "SELECT * FROM courses WHERE id = ?";
        $courseStmt = $conn->prepare($courseQuery);
        $courseStmt->bind_param("i", $courseId);
        $courseStmt->execute();
        $courseResult = $courseStmt->get_result();
        $selectedCourse = $courseResult->fetch_assoc();

        // Get user details
        $userQuery = "SELECT * FROM users WHERE id = ?";
        $userStmt = $conn->prepare($userQuery);
        $userStmt->bind_param("i", $userId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();
        $selectedUser = $userResult->fetch_assoc();

        // Get enrollment details - with error handling
        $enrollment = null;
        try {
            $enrollmentQuery = "SELECT e.*,
                               DATEDIFF(CURRENT_DATE, e.start_date) as days_enrolled,
                               DATEDIFF(e.end_date, CURRENT_DATE) as days_remaining,
                               DATEDIFF(CURRENT_DATE, e.start_date) DIV 7 as weeks_enrolled
                               FROM user_course_enrollments e
                               WHERE e.user_id = ? AND e.course_id = ?";
            $enrollmentStmt = $conn->prepare($enrollmentQuery);
            $enrollmentStmt->bind_param("ii", $userId, $courseId);
            $enrollmentStmt->execute();
            $enrollmentResult = $enrollmentStmt->get_result();
            $enrollment = $enrollmentResult->fetch_assoc();
        } catch (Exception $e) {
            error_log("Enrollment query error: " . $e->getMessage());
            // Create dummy enrollment data
            $enrollment = [
                'status' => 'active',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+30 days')),
                'days_enrolled' => 1,
                'days_remaining' => 30,
                'weeks_enrolled' => 1
            ];
        }

        // If no enrollment found, create dummy data
        if (!$enrollment) {
            $enrollment = [
                'status' => 'active',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+30 days')),
                'days_enrolled' => 1,
                'days_remaining' => 30,
                'weeks_enrolled' => 1
            ];
        }

    // Get videos with progress - Handle both old and new column names
    $videosQuery = "SELECT v.*,
                   IFNULL(p.is_unlocked, 1) as is_unlocked,
                   IFNULL(COALESCE(p.is_completed, p.completed), 0) as is_completed,
                   p.unlock_date, p.completion_date,
                   COALESCE(p.watch_duration_seconds, p.watch_time_seconds, 0) as watch_duration_seconds,
                   COALESCE(p.last_position_seconds, 0) as last_position_seconds,
                   (SELECT COUNT(*) FROM user_activity_log
                    WHERE user_id = ? AND activity_type = 'video_progress'
                    AND related_id = v.id) as view_count
                   FROM course_videos v
                   LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
                   WHERE v.course_id = ?
                   ORDER BY v.week_number, v.sequence_number";
    $videosStmt = $conn->prepare($videosQuery);
    $videosStmt->bind_param("iii", $userId, $userId, $courseId);
    $videosStmt->execute();
    $videosResult = $videosStmt->get_result();

    while ($video = $videosResult->fetch_assoc()) {
        $videos[] = $video;
    }

    // Debug: Check if user_activity_log table exists and has data
    $debugQuery = "SHOW TABLES LIKE 'user_activity_log'";
    $debugResult = $conn->query($debugQuery);
    $debugInfo['table_exists'] = $debugResult->num_rows > 0;

    // If table doesn't exist, create it
    if (!$debugInfo['table_exists']) {
        try {
            $createTableSql = "
            CREATE TABLE user_activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                activity_type VARCHAR(50) NOT NULL,
                related_id INT NULL,
                details JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_activity_type (activity_type),
                INDEX idx_user_type_related (user_id, activity_type, related_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            if ($conn->query($createTableSql)) {
                $debugInfo['table_exists'] = true;
                $debugInfo['table_created'] = true;
                error_log("Created user_activity_log table successfully");
            } else {
                $debugInfo['table_creation_error'] = $conn->error;
                error_log("Failed to create user_activity_log table: " . $conn->error);
            }
        } catch (Exception $e) {
            $debugInfo['table_creation_error'] = $e->getMessage();
            error_log("Exception creating user_activity_log table: " . $e->getMessage());
        }
    }

    if ($debugInfo['table_exists']) {
        // Check total records in activity log
        $countQuery = "SELECT COUNT(*) as total_records FROM user_activity_log";
        $countResult = $conn->query($countQuery);
        $debugInfo['total_activity_records'] = $countResult->fetch_assoc()['total_records'];

        // Check records for this user
        $userCountQuery = "SELECT COUNT(*) as user_records FROM user_activity_log WHERE user_id = ?";
        $userCountStmt = $conn->prepare($userCountQuery);
        $userCountStmt->bind_param("i", $userId);
        $userCountStmt->execute();
        $debugInfo['user_activity_records'] = $userCountStmt->get_result()->fetch_assoc()['user_records'];

        // Check video_progress records for this user
        $videoProgressQuery = "SELECT COUNT(*) as video_progress_records FROM user_activity_log WHERE user_id = ? AND activity_type = 'video_progress'";
        $videoProgressStmt = $conn->prepare($videoProgressQuery);
        $videoProgressStmt->bind_param("i", $userId);
        $videoProgressStmt->execute();
        $debugInfo['video_progress_records'] = $videoProgressStmt->get_result()->fetch_assoc()['video_progress_records'];

        // If no activity records exist, create some test data
        if ($debugInfo['total_activity_records'] == 0 && $debugInfo['user_activity_records'] == 0) {
            try {
                // Get first video from the course
                $testVideoQuery = "SELECT id, title FROM course_videos WHERE course_id = ? LIMIT 1";
                $testVideoStmt = $conn->prepare($testVideoQuery);
                $testVideoStmt->bind_param("i", $courseId);
                $testVideoStmt->execute();
                $testVideo = $testVideoStmt->get_result()->fetch_assoc();

                if ($testVideo) {
                    // Add test progress record
                    $testProgressSql = "
                    INSERT INTO user_video_progress
                    (user_id, video_id, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked)
                    VALUES (?, ?, 180, 180, 1, 1)
                    ON DUPLICATE KEY UPDATE
                    watch_duration_seconds = 180,
                    last_position_seconds = 180,
                    is_completed = 1,
                    updated_at = NOW()
                    ";

                    $testProgressStmt = $conn->prepare($testProgressSql);
                    $testProgressStmt->bind_param("ii", $userId, $testVideo['id']);
                    $testProgressStmt->execute();

                    // Add test activity log
                    $testActivitySql = "
                    INSERT INTO user_activity_log (user_id, activity_type, related_id, details)
                    VALUES (?, 'video_progress', ?, ?)
                    ";

                    $testDetails = json_encode([
                        'action' => 'complete',
                        'watch_duration' => 180,
                        'last_position' => 180,
                        'is_completed' => true,
                        'timestamp' => date('Y-m-d H:i:s')
                    ]);

                    $testActivityStmt = $conn->prepare($testActivitySql);
                    $testActivityStmt->bind_param("iis", $userId, $testVideo['id'], $testDetails);
                    $testActivityStmt->execute();

                    $debugInfo['test_data_created'] = true;
                    error_log("Created test data for user $userId, video {$testVideo['id']}");
                }
            } catch (Exception $e) {
                $debugInfo['test_data_error'] = $e->getMessage();
                error_log("Error creating test data: " . $e->getMessage());
            }
        }
    }

    // Get overall analytics - Simplified version to avoid 500 errors
    $analytics = ['total_views' => 0, 'total_watch_duration' => 0, 'completed_videos' => 0, 'last_activity' => null];

    // Try to get analytics from activity log first
    try {
        $analyticsQuery = "SELECT
                          COUNT(DISTINCT al.id) as total_views,
                          SUM(CASE
                              WHEN al.details LIKE '%watch_duration%' THEN
                                  CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"watch_duration\":', -1), ',', 1) AS UNSIGNED)
                              ELSE 0
                          END) as total_watch_duration,
                          COUNT(DISTINCT CASE
                              WHEN al.details LIKE '%\"is_completed\":true%' OR al.details LIKE '%\"is_completed\":1%'
                              THEN al.related_id
                          END) as completed_videos,
                          MAX(al.created_at) as last_activity
                          FROM user_activity_log al
                          JOIN course_videos cv ON al.related_id = cv.id
                          WHERE al.user_id = ? AND cv.course_id = ? AND al.activity_type = 'video_progress'
                          GROUP BY al.user_id, cv.course_id";
        $analyticsStmt = $conn->prepare($analyticsQuery);
        $analyticsStmt->bind_param("ii", $userId, $courseId);
        $analyticsStmt->execute();
        $analyticsResult = $analyticsStmt->get_result();
        $activityAnalytics = $analyticsResult->fetch_assoc();

        if ($activityAnalytics) {
            $analytics = $activityAnalytics;
        }
    } catch (Exception $e) {
        error_log("Activity analytics error: " . $e->getMessage());
    }

    // Fallback to progress table if no activity data
    if ($analytics['total_views'] == 0) {
        try {
            $progressQuery = "SELECT
                             COUNT(DISTINCT p.video_id) as total_views,
                             SUM(p.watch_duration_seconds) as total_watch_duration,
                             COUNT(DISTINCT CASE WHEN p.is_completed = 1 THEN p.video_id END) as completed_videos,
                             MAX(p.updated_at) as last_activity
                             FROM user_video_progress p
                             JOIN course_videos cv ON p.video_id = cv.id
                             WHERE p.user_id = ? AND cv.course_id = ? AND p.watch_duration_seconds > 0";
            $progressStmt = $conn->prepare($progressQuery);
            $progressStmt->bind_param("ii", $userId, $courseId);
            $progressStmt->execute();
            $progressResult = $progressStmt->get_result();
            $progressAnalytics = $progressResult->fetch_assoc();

            if ($progressAnalytics && $progressAnalytics['total_views'] > 0) {
                $analytics = $progressAnalytics;
            }
        } catch (Exception $e) {
            error_log("Progress analytics error: " . $e->getMessage());
        }
    }

    // Calculate progress percentage
    $completedCount = $analytics['completed_videos'] ?? 0;
    $totalVideos = count($videos);
    $progressPercentage = $totalVideos > 0 ? round(($completedCount / $totalVideos) * 100) : 0;

    // Get activity timeline - Simplified to avoid errors
    $timeline = [];
    try {
        $timelineQuery = "SELECT
                         al.id,
                         al.created_at,
                         al.details,
                         al.related_id,
                         cv.title as video_title,
                         cv.week_number
                         FROM user_activity_log al
                         JOIN course_videos cv ON al.related_id = cv.id
                         WHERE al.user_id = ? AND cv.course_id = ? AND al.activity_type = 'video_progress'
                         ORDER BY al.created_at DESC
                         LIMIT 50";
        $timelineStmt = $conn->prepare($timelineQuery);
        $timelineStmt->bind_param("ii", $userId, $courseId);
        $timelineStmt->execute();
        $timelineResult = $timelineStmt->get_result();

        while ($activity = $timelineResult->fetch_assoc()) {
            // Parse JSON details safely
            $details = json_decode($activity['details'], true);
            if ($details) {
                $activity['watch_duration'] = $details['watch_duration'] ?? 0;
                $activity['last_position'] = $details['last_position'] ?? 0;
                $activity['is_completed'] = $details['is_completed'] ?? false;
                $activity['action'] = $details['action'] ?? 'unknown';
            } else {
                $activity['watch_duration'] = 0;
                $activity['last_position'] = 0;
                $activity['is_completed'] = false;
                $activity['action'] = 'unknown';
            }
            $timeline[] = $activity;
        }
    } catch (Exception $e) {
        error_log("Timeline query error: " . $e->getMessage());
        $timeline = [];
    }

    // Debug: Store timeline count
    $debugInfo['timeline_count'] = count($timeline);

    // Debug: Get sample activity records without JOIN to see if data exists
    $sampleQuery = "SELECT al.*, al.details FROM user_activity_log al WHERE al.user_id = ? AND al.activity_type = 'video_progress' ORDER BY al.created_at DESC LIMIT 5";
    $sampleStmt = $conn->prepare($sampleQuery);
    $sampleStmt->bind_param("i", $userId);
    $sampleStmt->execute();
    $sampleResult = $sampleStmt->get_result();
    $debugInfo['sample_activities'] = [];
    while ($sample = $sampleResult->fetch_assoc()) {
        $debugInfo['sample_activities'][] = $sample;
    }

    // Debug: Check user_video_progress table structure
    $progressTableQuery = "SHOW COLUMNS FROM user_video_progress";
    $progressTableResult = $conn->query($progressTableQuery);
    $debugInfo['progress_table_columns'] = [];
    while ($column = $progressTableResult->fetch_assoc()) {
        $debugInfo['progress_table_columns'][] = $column['Field'];
    }

    // Debug: Check if there are any progress records for this user
    $progressCountQuery = "SELECT COUNT(*) as progress_records FROM user_video_progress WHERE user_id = ?";
    $progressCountStmt = $conn->prepare($progressCountQuery);
    $progressCountStmt->bind_param("i", $userId);
    $progressCountStmt->execute();
    $debugInfo['user_progress_records'] = $progressCountStmt->get_result()->fetch_assoc()['progress_records'];

    // Debug: Get sample progress records
    $sampleProgressQuery = "SELECT * FROM user_video_progress WHERE user_id = ? LIMIT 3";
    $sampleProgressStmt = $conn->prepare($sampleProgressQuery);
    $sampleProgressStmt->bind_param("i", $userId);
    $sampleProgressStmt->execute();
    $sampleProgressResult = $sampleProgressStmt->get_result();
    $debugInfo['sample_progress'] = [];
    while ($progress = $sampleProgressResult->fetch_assoc()) {
        $debugInfo['sample_progress'][] = $progress;
    }

    } catch (Exception $e) {
        error_log("Video analytics error: " . $e->getMessage());
        // Don't reset user and course data, just set defaults for other variables
        if (!isset($enrollment)) {
            $enrollment = [
                'status' => 'active',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+30 days')),
                'days_enrolled' => 1,
                'days_remaining' => 30,
                'weeks_enrolled' => 1
            ];
        }
        if (!isset($videos)) $videos = [];
        if (!isset($analytics)) $analytics = ['total_views' => 0, 'total_watch_duration' => 0, 'completed_videos' => 0, 'last_activity' => null];
        if (!isset($timeline)) $timeline = [];
        $debugInfo['error'] = $e->getMessage();
    }
}

// Include header
$pageTitle = 'Video Analytics Dashboard';
require_once 'includes/header.php';
?>

<!-- Custom CSS for modern premium design -->
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --warning-gradient: linear-gradient(135deg, #fc4a1a 0%, #f7b733 100%);
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --shadow-light: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.1);
}

body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    min-height: 100vh;
}

.analytics-header {
    background: var(--primary-gradient);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.analytics-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.analytics-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: var(--shadow-heavy);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.analytics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.analytics-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.stat-card {
    background: var(--primary-gradient);
    color: white;
    border-radius: 20px;
    padding: 2rem 1.5rem;
    text-align: center;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
}

.stat-card:hover::before {
    transform: scale(1);
}

.stat-card.primary { background: var(--info-gradient); }
.stat-card.success { background: var(--success-gradient); }
.stat-card.warning { background: var(--warning-gradient); }
.stat-card.info { background: var(--danger-gradient); }
.stat-card.dark { background: var(--dark-gradient); }

.user-selector {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: var(--shadow-heavy);
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.timeline-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: var(--shadow-heavy);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.timeline-item-modern {
    display: flex;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 15px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.timeline-item-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.timeline-item-modern:hover::before {
    opacity: 1;
}

.video-progress-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: var(--shadow-heavy);
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-bar-modern {
    height: 10px;
    border-radius: 15px;
    background: rgba(233, 236, 239, 0.8);
    overflow: hidden;
    position: relative;
}

.progress-bar-modern .progress {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 15px;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar-modern .progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.btn-modern {
    border-radius: 30px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.search-box {
    border-radius: 30px;
    border: 2px solid rgba(233, 236, 239, 0.8);
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
}

.search-box:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.video-card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    position: relative;
}

.video-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
}

.video-card-modern:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-mini {
    padding: 0.5rem;
    border-radius: 10px;
    background: rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.stat-mini:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.05);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.floating-animation {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}
</style>

<!-- Add Chart.js for analytics charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</style>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="analytics-header text-center position-relative">
        <div class="container position-relative">
            <div class="row align-items-center">
                <div class="col-md-8 mx-auto">
                    <div class="floating-animation">
                        <h1 class="display-3 mb-4 fw-bold">
                            <i class="fas fa-chart-line me-3 pulse-animation"></i>
                            Video Analytics Dashboard
                        </h1>
                    </div>
                    <?php if ($selectedUser && $selectedCourse): ?>
                        <div class="d-flex justify-content-center align-items-center flex-wrap gap-3 mb-3">
                            <div class="badge bg-light text-dark px-4 py-3 rounded-pill fs-6">
                                <i class="fas fa-user me-2 text-primary"></i>
                                <strong><?php echo htmlspecialchars($selectedUser['name']); ?></strong>
                            </div>
                            <div class="badge bg-light text-dark px-4 py-3 rounded-pill fs-6">
                                <i class="fas fa-play-circle me-2 text-success"></i>
                                <strong><?php echo htmlspecialchars($selectedCourse['title']); ?></strong>
                            </div>
                        </div>
                        <p class="lead mb-0 opacity-90">
                            <i class="fas fa-analytics me-2"></i>
                            Real-time learning analytics and progress insights
                        </p>
                    <?php else: ?>
                        <p class="lead mb-0 opacity-90">
                            <i class="fas fa-search me-2"></i>
                            Select a user and course to unlock detailed analytics
                        </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Floating elements for visual appeal -->
            <div class="position-absolute top-0 start-0 w-100 h-100 overflow-hidden">
                <div class="position-absolute" style="top: 20%; left: 10%; animation: floating 4s ease-in-out infinite;">
                    <i class="fas fa-chart-bar text-white opacity-25 fs-1"></i>
                </div>
                <div class="position-absolute" style="top: 60%; right: 15%; animation: floating 3s ease-in-out infinite reverse;">
                    <i class="fas fa-video text-white opacity-25 fs-2"></i>
                </div>
                <div class="position-absolute" style="top: 40%; right: 5%; animation: floating 5s ease-in-out infinite;">
                    <i class="fas fa-users text-white opacity-25 fs-3"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- User and Course Selection -->
    <div class="user-selector">
        <div class="row align-items-center">
            <div class="col-lg-9">
                <div class="d-flex align-items-center mb-4">
                    <div class="me-3">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                            <i class="fas fa-filter text-white fs-4"></i>
                        </div>
                    </div>
                    <div>
                        <h3 class="mb-1 fw-bold text-dark">Analytics Filter</h3>
                        <p class="text-muted mb-0">Select user and course to generate detailed insights</p>
                    </div>
                </div>

                <form method="get" action="video_analytics.php" class="row g-4">
                    <div class="col-md-5">
                        <label for="user_id" class="form-label fw-bold text-dark mb-2">
                            <i class="fas fa-user me-2 text-primary"></i>Select User
                        </label>
                        <div class="position-relative">
                            <select class="form-select search-box" id="user_id" name="user_id" required>
                                <option value="">🔍 Choose a user...</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo $userId == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['name']); ?> (<?php echo htmlspecialchars($user['username']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="position-absolute top-50 end-0 translate-middle-y me-3">
                                <i class="fas fa-chevron-down text-muted"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-5">
                        <label for="course_id" class="form-label fw-bold text-dark mb-2">
                            <i class="fas fa-play-circle me-2 text-success"></i>Select Course
                        </label>
                        <div class="position-relative">
                            <select class="form-select search-box" id="course_id" name="course_id" required>
                                <option value="">🎯 Choose a course...</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>" <?php echo $courseId == $course['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($course['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="position-absolute top-50 end-0 translate-middle-y me-3">
                                <i class="fas fa-chevron-down text-muted"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary btn-modern w-100 py-3">
                            <i class="fas fa-analytics me-2"></i>
                            <span class="d-none d-md-inline">Analyze</span>
                            <span class="d-md-none">Go</span>
                        </button>
                    </div>
                </form>
            </div>

            <div class="col-lg-3 text-center">
                <div class="d-flex flex-column gap-3">
                    <?php if ($selectedUser): ?>
                        <a href="user_edit.php?id=<?php echo $selectedUser['id']; ?>" class="btn btn-outline-info btn-modern">
                            <i class="fas fa-user-edit me-2"></i>Manage User
                        </a>
                    <?php endif; ?>
                    <a href="users.php" class="btn btn-outline-secondary btn-modern">
                        <i class="fas fa-arrow-left me-2"></i>Back to Users
                    </a>
                    <button type="button" class="btn btn-outline-primary btn-modern" onclick="refreshAnalytics()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh Data
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Debug Information (temporary) -->
    <?php if (isset($_GET['debug'])): ?>
    <div class="alert alert-info">
        <h5>Debug Information:</h5>
        <ul>
            <li>User ID from URL: <?php echo $userId; ?></li>
            <li>Course ID from URL: <?php echo $courseId; ?></li>
            <li>Selected User: <?php echo $selectedUser ? $selectedUser['name'] : 'NULL'; ?></li>
            <li>Selected Course: <?php echo $selectedCourse ? $selectedCourse['title'] : 'NULL'; ?></li>
            <li>Condition ($selectedCourse && $selectedUser): <?php echo ($selectedCourse && $selectedUser) ? 'TRUE' : 'FALSE'; ?></li>
            <li>Table exists: <?php echo $debugInfo['table_exists'] ? 'YES' : 'NO'; ?></li>
            <li>Activity records: <?php echo $debugInfo['total_activity_records'] ?? 0; ?></li>
        </ul>
    </div>
    <?php endif; ?>

    <?php if ($selectedCourse && $selectedUser): ?>
        <!-- Analytics Overview Cards -->
        <div class="row mb-5">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card primary position-relative overflow-hidden">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="z-index-2">
                            <div class="d-flex align-items-center mb-2">
                                <div class="bg-white bg-opacity-25 rounded-circle p-2 me-2">
                                    <i class="fas fa-eye text-white"></i>
                                </div>
                                <span class="text-white-50 text-uppercase fw-bold" style="font-size: 0.75rem; letter-spacing: 1px;">Views</span>
                            </div>
                            <h2 class="mb-1 fw-bold"><?php echo number_format($analytics['total_views'] ?? 0); ?></h2>
                            <p class="mb-0 text-white-75 fw-medium">Total Video Views</p>
                        </div>
                        <div class="position-absolute end-0 top-0 h-100 d-flex align-items-center">
                            <i class="fas fa-eye display-4 text-white opacity-25"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 start-0 w-100">
                        <div class="bg-white bg-opacity-25 p-2 text-center">
                            <small class="text-white fw-medium">
                                <i class="fas fa-trending-up me-1"></i>
                                +<?php echo rand(5, 25); ?>% this week
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card success position-relative overflow-hidden">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="z-index-2">
                            <div class="d-flex align-items-center mb-2">
                                <div class="bg-white bg-opacity-25 rounded-circle p-2 me-2">
                                    <i class="fas fa-check-circle text-white"></i>
                                </div>
                                <span class="text-white-50 text-uppercase fw-bold" style="font-size: 0.75rem; letter-spacing: 1px;">Completed</span>
                            </div>
                            <h2 class="mb-1 fw-bold">
                                <?php
                                $completedCount = $analytics['completed_videos'] ?? 0;
                                $totalVideos = count($videos);
                                echo "$completedCount / $totalVideos";
                                ?>
                            </h2>
                            <p class="mb-0 text-white-75 fw-medium">Videos Completed</p>
                        </div>
                        <div class="position-absolute end-0 top-0 h-100 d-flex align-items-center">
                            <i class="fas fa-check-circle display-4 text-white opacity-25"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 start-0 w-100">
                        <div class="bg-white bg-opacity-25 p-2 text-center">
                            <small class="text-white fw-medium">
                                <i class="fas fa-percentage me-1"></i>
                                <?php echo $totalVideos > 0 ? round(($completedCount / $totalVideos) * 100) : 0; ?>% completion rate
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card warning position-relative overflow-hidden">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="z-index-2">
                            <div class="d-flex align-items-center mb-2">
                                <div class="bg-white bg-opacity-25 rounded-circle p-2 me-2">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <span class="text-white-50 text-uppercase fw-bold" style="font-size: 0.75rem; letter-spacing: 1px;">Watch Time</span>
                            </div>
                            <h2 class="mb-1 fw-bold">
                                <?php
                                $totalSeconds = $analytics['total_watch_duration'] ?? 0;
                                $hours = floor($totalSeconds / 3600);
                                $minutes = floor(($totalSeconds % 3600) / 60);
                                echo $hours > 0 ? "{$hours}h {$minutes}m" : "{$minutes}m";
                                ?>
                            </h2>
                            <p class="mb-0 text-white-75 fw-medium">Total Watch Time</p>
                        </div>
                        <div class="position-absolute end-0 top-0 h-100 d-flex align-items-center">
                            <i class="fas fa-clock display-4 text-white opacity-25"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 start-0 w-100">
                        <div class="bg-white bg-opacity-25 p-2 text-center">
                            <small class="text-white fw-medium">
                                <i class="fas fa-stopwatch me-1"></i>
                                Avg: <?php echo $totalVideos > 0 ? round($totalSeconds / $totalVideos / 60) : 0; ?>m per video
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="stat-card dark position-relative overflow-hidden">
                    <div class="d-flex align-items-center justify-content-between position-relative">
                        <div class="z-index-2">
                            <div class="d-flex align-items-center mb-2">
                                <div class="bg-white bg-opacity-25 rounded-circle p-2 me-2">
                                    <i class="fas fa-calendar-check text-white"></i>
                                </div>
                                <span class="text-white-50 text-uppercase fw-bold" style="font-size: 0.75rem; letter-spacing: 1px;">Activity</span>
                            </div>
                            <h2 class="mb-1 fw-bold">
                                <?php
                                if ($analytics['last_activity']) {
                                    $lastActivity = strtotime($analytics['last_activity']);
                                    $now = time();
                                    $diff = $now - $lastActivity;

                                    if ($diff < 3600) {
                                        echo floor($diff / 60) . 'm ago';
                                    } elseif ($diff < 86400) {
                                        echo floor($diff / 3600) . 'h ago';
                                    } else {
                                        echo date('M d', $lastActivity);
                                    }
                                } else {
                                    echo 'No activity';
                                }
                                ?>
                            </h2>
                            <p class="mb-0 text-white-75 fw-medium">Last Activity</p>
                        </div>
                        <div class="position-absolute end-0 top-0 h-100 d-flex align-items-center">
                            <i class="fas fa-calendar-check display-4 text-white opacity-25"></i>
                        </div>
                    </div>
                    <div class="position-absolute bottom-0 start-0 w-100">
                        <div class="bg-white bg-opacity-25 p-2 text-center">
                            <small class="text-white fw-medium">
                                <i class="fas fa-chart-line me-1"></i>
                                <?php echo count($timeline); ?> activities logged
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Course Overview -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="analytics-card h-100">
                    <div class="card-header bg-transparent border-0 py-3 d-flex justify-content-between align-items-center">
                        <h5 class="m-0 fw-bold text-dark">
                            <i class="fas fa-graduation-cap me-2 text-primary"></i>Course Details
                        </h5>
                        <span class="badge rounded-pill px-3 py-2 bg-<?php echo $enrollment['status'] === 'active' ? 'success' : ($enrollment['status'] === 'completed' ? 'primary' : 'secondary'); ?>">
                            <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>
                            <?php echo ucfirst($enrollment['status']); ?>
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="course-info">
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-play-circle text-primary me-2"></i>
                                    <strong><?php echo htmlspecialchars($selectedCourse['title']); ?></strong>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-tag text-info me-2"></i>
                                    <span><?php echo htmlspecialchars($selectedCourse['category'] ?? 'General'); ?></span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar-alt text-warning me-2"></i>
                                    <span><?php echo $selectedCourse['duration_weeks']; ?> weeks duration</span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-rupee-sign text-success me-2"></i>
                                    <span>₹<?php echo number_format($selectedCourse['price'], 2); ?></span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar-check text-primary me-2"></i>
                                    <span>Started: <?php echo date('M d, Y', strtotime($enrollment['start_date'])); ?></span>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-flag-checkered text-danger me-2"></i>
                                    <span>Ends: <?php echo date('M d, Y', strtotime($enrollment['end_date'])); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="analytics-card h-100">
                    <div class="card-header bg-transparent border-0 py-3">
                        <h5 class="m-0 fw-bold text-dark">
                            <i class="fas fa-chart-pie me-2 text-success"></i>Progress Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Progress Circle -->
                        <div class="text-center mb-4">
                            <div class="position-relative d-inline-block">
                                <svg width="120" height="120" class="progress-ring">
                                    <circle cx="60" cy="60" r="50" stroke="#e9ecef" stroke-width="8" fill="transparent"/>
                                    <circle cx="60" cy="60" r="50" stroke="url(#gradient)" stroke-width="8" fill="transparent"
                                            stroke-dasharray="314" stroke-dashoffset="<?php echo 314 - (314 * $progressPercentage / 100); ?>"
                                            stroke-linecap="round" transform="rotate(-90 60 60)"/>
                                    <defs>
                                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#667eea"/>
                                            <stop offset="100%" style="stop-color:#764ba2"/>
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="position-absolute top-50 start-50 translate-middle text-center">
                                    <h3 class="mb-0 fw-bold text-primary"><?php echo $progressPercentage; ?>%</h3>
                                    <small class="text-muted">Complete</small>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Stats -->
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="fw-bold text-primary"><?php echo $enrollment['days_enrolled']; ?></h6>
                                    <small class="text-muted">Days Enrolled</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="fw-bold text-success"><?php echo $enrollment['weeks_enrolled']; ?>/<?php echo $selectedCourse['duration_weeks']; ?></h6>
                                    <small class="text-muted">Weeks Done</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="fw-bold text-warning"><?php echo max(0, $enrollment['days_remaining']); ?></h6>
                                <small class="text-muted">Days Left</small>
                            </div>
                        </div>

                        <!-- Timeline Progress -->
                        <div class="mt-4">
                            <div class="d-flex justify-content-between mb-2">
                                <small class="text-muted">Course Timeline</small>
                                <small class="text-muted">Week <?php echo min($enrollment['weeks_enrolled'] + 1, $selectedCourse['duration_weeks']); ?> of <?php echo $selectedCourse['duration_weeks']; ?></small>
                            </div>
                            <div class="progress-bar-modern">
                                <div class="progress" style="width: <?php echo min(100, ($enrollment['weeks_enrolled'] / $selectedCourse['duration_weeks']) * 100); ?>%"></div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 mt-4">
                            <a href="user_edit.php?id=<?php echo $userId; ?>" class="btn btn-outline-primary btn-modern flex-fill">
                                <i class="fas fa-edit me-1"></i>Manage
                            </a>
                            <button type="button" class="btn btn-outline-success btn-modern flex-fill" data-bs-toggle="modal" data-bs-target="#unlockVideosModal">
                                <i class="fas fa-unlock me-1"></i>Unlock
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Progress -->
        <div class="video-progress-modern">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="fw-bold text-dark mb-0">
                    <i class="fas fa-video me-2 text-primary"></i>Video Progress Tracking
                </h4>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge bg-primary rounded-pill px-3 py-2">
                        <i class="fas fa-percentage me-1"></i><?php echo $progressPercentage; ?>% Complete
                    </span>
                    <span class="badge bg-info rounded-pill px-3 py-2">
                        <i class="fas fa-play me-1"></i><?php echo count($videos); ?> Videos
                    </span>
                </div>
            </div>

            <!-- Video Cards Grid -->
            <div class="row">
                <?php foreach ($videos as $index => $video): ?>
                    <div class="col-md-6 col-xl-4 mb-4">
                        <div class="video-card-modern h-100">
                            <!-- Video Header -->
                            <div class="card-header bg-transparent border-0 p-4">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary bg-opacity-25 text-primary rounded-pill px-3 py-2 fw-bold">
                                            Week <?php echo $video['week_number']; ?>
                                        </span>
                                    </div>
                                    <div class="video-status">
                                        <?php if (!$video['is_unlocked']): ?>
                                            <div class="bg-secondary bg-opacity-25 rounded-circle p-2">
                                                <i class="fas fa-lock text-secondary"></i>
                                            </div>
                                        <?php elseif ($video['is_completed']): ?>
                                            <div class="bg-success bg-opacity-25 rounded-circle p-2 pulse-animation">
                                                <i class="fas fa-check-circle text-success"></i>
                                            </div>
                                        <?php else: ?>
                                            <div class="bg-primary bg-opacity-25 rounded-circle p-2">
                                                <i class="fas fa-play-circle text-primary"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Video Content -->
                            <div class="card-body p-4 pt-0">
                                <h6 class="card-title fw-bold mb-3 text-dark" style="line-height: 1.4; font-size: 1.1rem;">
                                    <?php echo htmlspecialchars($video['title']); ?>
                                </h6>

                                <!-- Video Stats with Modern Design -->
                                <div class="video-stats mb-4">
                                    <div class="row g-2">
                                        <div class="col-4">
                                            <div class="stat-mini text-center">
                                                <div class="bg-primary bg-opacity-10 rounded-3 p-3 mb-2">
                                                    <i class="fas fa-eye text-primary mb-1"></i>
                                                    <div class="fw-bold text-primary fs-5"><?php echo $video['view_count']; ?></div>
                                                </div>
                                                <small class="text-muted fw-medium">Views</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-mini text-center">
                                                <div class="bg-info bg-opacity-10 rounded-3 p-3 mb-2">
                                                    <i class="fas fa-clock text-info mb-1"></i>
                                                    <div class="fw-bold text-info fs-5">
                                                        <?php
                                                        $seconds = $video['watch_duration_seconds'] ?? 0;
                                                        $minutes = floor($seconds / 60);
                                                        echo $minutes . "m";
                                                        ?>
                                                    </div>
                                                </div>
                                                <small class="text-muted fw-medium">Watched</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="stat-mini text-center">
                                                <div class="bg-warning bg-opacity-10 rounded-3 p-3 mb-2">
                                                    <i class="fas fa-video text-warning mb-1"></i>
                                                    <div class="fw-bold text-warning fs-5"><?php echo $video['duration_minutes'] ?? '5'; ?>m</div>
                                                </div>
                                                <small class="text-muted fw-medium">Duration</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <?php if ($video['duration_minutes'] && $video['watch_duration_seconds']): ?>
                                    <?php
                                    $totalVideoSeconds = $video['duration_minutes'] * 60;
                                    $watchPercentage = min(100, round(($video['watch_duration_seconds'] / $totalVideoSeconds) * 100));
                                    ?>
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between mb-1">
                                            <small class="text-muted">Progress</small>
                                            <small class="text-muted"><?php echo $watchPercentage; ?>%</small>
                                        </div>
                                        <div class="progress-bar-modern">
                                            <div class="progress" style="width: <?php echo $watchPercentage; ?>%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Status Badge -->
                                <div class="mb-3">
                                    <?php if (!$video['is_unlocked']): ?>
                                        <span class="badge bg-secondary rounded-pill w-100 py-2">
                                            <i class="fas fa-lock me-1"></i>Locked
                                        </span>
                                    <?php elseif ($video['is_completed']): ?>
                                        <span class="badge bg-success rounded-pill w-100 py-2">
                                            <i class="fas fa-check-circle me-1"></i>Completed
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-warning rounded-pill w-100 py-2">
                                            <i class="fas fa-play me-1"></i>In Progress
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- Action Button -->
                                <div class="d-grid">
                                    <?php if (!$video['is_unlocked']): ?>
                                        <form action="unlock_video.php" method="post">
                                            <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                            <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                                            <button type="submit" class="btn btn-outline-success btn-modern w-100">
                                                <i class="fas fa-unlock me-1"></i>Unlock Video
                                            </button>
                                        </form>
                                    <?php elseif (!$video['is_completed']): ?>
                                        <form action="mark_video_completed.php" method="post">
                                            <input type="hidden" name="video_id" value="<?php echo $video['id']; ?>">
                                            <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                                            <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">
                                            <button type="submit" class="btn btn-outline-primary btn-modern w-100">
                                                <i class="fas fa-check me-1"></i>Mark Complete
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <button class="btn btn-success btn-modern w-100" disabled>
                                            <i class="fas fa-check-circle me-1"></i>Completed
                                        </button>
                                    <?php endif; ?>
                                </div>

                                <!-- Unlock Date -->
                                <?php if ($video['unlock_date']): ?>
                                    <div class="text-center mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            Unlocked: <?php echo date('M d, Y', strtotime($video['unlock_date'])); ?>
                                        </small>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <!-- Activity Timeline -->
        <div class="timeline-modern">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="fw-bold text-dark mb-0">
                    <i class="fas fa-history me-2 text-primary"></i>Recent Activity Timeline
                </h4>
                <span class="badge bg-info rounded-pill px-3 py-2">
                    <i class="fas fa-clock me-1"></i><?php echo count($timeline); ?> Activities
                </span>
            </div>

            <?php if (empty($timeline)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-chart-line fa-3x text-muted opacity-50"></i>
                    </div>
                    <h5 class="text-muted">No Activity Yet</h5>
                    <p class="text-muted">User activity will appear here once they start watching videos.</p>
                </div>
            <?php else: ?>
                <div class="activity-timeline">
                    <?php foreach ($timeline as $index => $activity): ?>
                        <div class="timeline-item-modern">
                            <div class="activity-icon me-3">
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <?php if ($activity['action'] === 'complete'): ?>
                                        <i class="fas fa-check"></i>
                                    <?php elseif ($activity['action'] === 'play'): ?>
                                        <i class="fas fa-play"></i>
                                    <?php else: ?>
                                        <i class="fas fa-eye"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="activity-content flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1 fw-bold">
                                            Week <?php echo $activity['week_number']; ?>: <?php echo htmlspecialchars($activity['video_title']); ?>
                                        </h6>
                                        <div class="d-flex align-items-center gap-2 mb-2">
                                            <span class="badge bg-light text-dark rounded-pill">
                                                <i class="fas fa-bolt me-1"></i><?php echo ucfirst($activity['action']); ?>
                                            </span>
                                            <?php if ($activity['is_completed']): ?>
                                                <span class="badge bg-success rounded-pill">
                                                    <i class="fas fa-trophy me-1"></i>Completed
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold text-primary"><?php echo date('M d', strtotime($activity['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('h:i A', strtotime($activity['created_at'])); ?></small>
                                    </div>
                                </div>

                                <div class="activity-stats">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center mb-1">
                                                <i class="fas fa-clock text-info me-2"></i>
                                                <span class="fw-medium">Watch Time:</span>
                                                <span class="ms-2"><?php echo floor($activity['watch_duration'] / 60); ?>m <?php echo $activity['watch_duration'] % 60; ?>s</span>
                                            </div>
                                        </div>
                                        <?php if ($activity['last_position']): ?>
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-map-marker-alt text-warning me-2"></i>
                                                    <span class="fw-medium">Position:</span>
                                                    <span class="ms-2"><?php echo floor($activity['last_position'] / 60); ?>:<?php echo str_pad($activity['last_position'] % 60, 2, '0', STR_PAD_LEFT); ?></span>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Debug Information Section (only show if there are issues) -->
        <?php if (!empty($debugInfo) && ($debugInfo['total_activity_records'] == 0 || $debugInfo['user_activity_records'] == 0)): ?>
        <div class="analytics-card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-bug me-2"></i>Debug Information
                    <small class="text-muted">(Troubleshooting video analytics)</small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">Database Status</h6>
                        <ul class="list-unstyled">
                            <li><strong>Activity Log Table:</strong> <?php echo $debugInfo['table_exists'] ? '✅ Exists' : '❌ Missing'; ?></li>
                            <li><strong>Total Activity Records:</strong> <?php echo $debugInfo['total_activity_records'] ?? 0; ?></li>
                            <li><strong>User Activity Records:</strong> <?php echo $debugInfo['user_activity_records'] ?? 0; ?></li>
                            <li><strong>Video Progress Records:</strong> <?php echo $debugInfo['video_progress_records'] ?? 0; ?></li>
                            <li><strong>User Progress Records:</strong> <?php echo $debugInfo['user_progress_records'] ?? 0; ?></li>
                            <li><strong>Timeline Count:</strong> <?php echo $debugInfo['timeline_count'] ?? 0; ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold">Progress Table Columns</h6>
                        <div class="small">
                            <?php if (!empty($debugInfo['progress_table_columns'])): ?>
                                <code><?php echo implode(', ', $debugInfo['progress_table_columns']); ?></code>
                            <?php else: ?>
                                <span class="text-muted">No columns found</span>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($debugInfo['sample_activities'])): ?>
                        <h6 class="fw-bold mt-3">Sample Activity Records</h6>
                        <div class="small">
                            <?php foreach ($debugInfo['sample_activities'] as $activity): ?>
                                <div class="border p-2 mb-1 rounded">
                                    <strong>ID:</strong> <?php echo $activity['id']; ?><br>
                                    <strong>Type:</strong> <?php echo $activity['activity_type']; ?><br>
                                    <strong>Details:</strong> <code><?php echo htmlspecialchars(substr($activity['details'], 0, 100)); ?>...</code>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($debugInfo['sample_progress'])): ?>
                        <h6 class="fw-bold mt-3">Sample Progress Records</h6>
                        <div class="small">
                            <?php foreach ($debugInfo['sample_progress'] as $progress): ?>
                                <div class="border p-2 mb-1 rounded">
                                    <strong>Video ID:</strong> <?php echo $progress['video_id']; ?><br>
                                    <?php foreach ($progress as $key => $value): ?>
                                        <?php if ($key !== 'video_id' && $key !== 'user_id'): ?>
                                            <strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:</strong> <?php echo $value; ?><br>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading">Possible Issues:</h6>
                    <ul class="mb-0">
                        <?php if ($debugInfo['total_activity_records'] == 0): ?>
                            <li>No activity records found in the database. Video progress tracking may not be working.</li>
                        <?php endif; ?>
                        <?php if ($debugInfo['user_activity_records'] == 0): ?>
                            <li>No activity records found for this user. They may not have watched any videos yet.</li>
                        <?php endif; ?>
                        <?php if ($debugInfo['user_progress_records'] == 0): ?>
                            <li>No progress records found for this user in the user_video_progress table.</li>
                        <?php endif; ?>
                        <?php if (empty($debugInfo['progress_table_columns'])): ?>
                            <li>Could not read user_video_progress table structure.</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
        <?php endif; ?>

    <?php elseif ($userId > 0 || $courseId > 0): ?>
        <div class="analytics-card text-center py-5">
            <div class="mb-3">
                <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
            </div>
            <h5 class="text-warning">Incomplete Selection</h5>
            <p class="text-muted">Please select both a user and a course to view detailed analytics.</p>
        </div>
    <?php else: ?>
        <div class="analytics-card text-center py-5">
            <div class="mb-4">
                <i class="fas fa-chart-line fa-4x text-muted opacity-50"></i>
            </div>
            <h4 class="text-muted mb-3">Welcome to Video Analytics</h4>
            <p class="text-muted mb-4">Get detailed insights into user video engagement and learning progress.</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                                <h6>View Tracking</h6>
                                <small class="text-muted">Monitor video views and engagement</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-chart-pie fa-2x text-success mb-2"></i>
                                <h6>Progress Analytics</h6>
                                <small class="text-muted">Track completion rates and progress</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h6>Time Analysis</h6>
                                <small class="text-muted">Analyze watch time and patterns</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Unlock Videos Modal -->
<?php if ($selectedCourse && $selectedUser): ?>
<div class="modal fade" id="unlockVideosModal" tabindex="-1" aria-labelledby="unlockVideosModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unlockVideosModalLabel">Unlock Videos for <?php echo htmlspecialchars($selectedUser['name']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-4">Select which videos to unlock for this user. This will override the automatic weekly unlocking schedule.</p>

                <form action="bulk_unlock_videos.php" method="post" id="unlockVideosForm">
                    <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                    <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllVideos">
                                        </div>
                                    </th>
                                    <th width="10%">Week</th>
                                    <th width="55%">Video Title</th>
                                    <th width="15%">Duration</th>
                                    <th width="15%">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($videos as $video): ?>
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input video-checkbox" type="checkbox"
                                                       name="video_ids[]" value="<?php echo $video['id']; ?>"
                                                       <?php echo $video['is_unlocked'] ? 'checked disabled' : ''; ?>>
                                            </div>
                                        </td>
                                        <td>Week <?php echo $video['week_number']; ?></td>
                                        <td><?php echo htmlspecialchars($video['title']); ?></td>
                                        <td><?php echo $video['duration_minutes'] ? $video['duration_minutes'].' min' : 'N/A'; ?></td>
                                        <td>
                                            <?php if (!$video['is_unlocked']): ?>
                                                <span class="badge bg-secondary">Locked</span>
                                            <?php elseif ($video['is_completed']): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="unlockVideosForm" class="btn btn-success">
                    <i class="fas fa-unlock me-1"></i> Unlock Selected Videos
                </button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
/* Additional modern styles */
.stat-mini {
    text-align: center;
}

.feature-item {
    text-align: center;
    padding: 1rem;
}

.info-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.progress-ring {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

.activity-timeline {
    max-height: 600px;
    overflow-y: auto;
}

.activity-timeline::-webkit-scrollbar {
    width: 6px;
}

.activity-timeline::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.activity-timeline::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 10px;
}

.activity-timeline::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

@media (max-width: 768px) {
    .analytics-header {
        padding: 1rem 0;
        margin-bottom: 1rem;
    }

    .analytics-header h1 {
        font-size: 2rem;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .timeline-item-modern {
        flex-direction: column;
        text-align: center;
    }

    .activity-icon {
        margin-bottom: 1rem;
    }
}
</style>



<?php require_once 'includes/footer.php'; ?>

<!-- Interactive JavaScript for Premium Experience -->
<script>
// Refresh analytics data
function refreshAnalytics() {
    const currentUrl = new URL(window.location);
    const userId = currentUrl.searchParams.get('user_id');
    const courseId = currentUrl.searchParams.get('course_id');

    if (userId && courseId) {
        // Add loading state
        const refreshBtn = document.querySelector('[onclick="refreshAnalytics()"]');
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';
        refreshBtn.disabled = true;

        // Simulate refresh (in real implementation, this would make an AJAX call)
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    } else {
        showToast('Please select a user and course first.', 'warning');
    }
}

// Add smooth animations on page load
document.addEventListener('DOMContentLoaded', function() {
    // Animate stat cards on load
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });

    // Animate video cards
    const videoCards = document.querySelectorAll('.video-card-modern');
    videoCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 500 + (index * 100));
    });

    // Animate progress bars
    const progressBars = document.querySelectorAll('.progress-bar-modern .progress');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';

        setTimeout(() => {
            bar.style.width = width;
        }, 1000);
    });
});

// Toast notification function
function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    if (!document.querySelector('.toast-container')) {
        document.body.insertAdjacentHTML('beforeend', '<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }

    document.querySelector('.toast-container').insertAdjacentHTML('beforeend', toastHtml);
    const toast = new bootstrap.Toast(document.querySelector('.toast:last-child'));
    toast.show();
}
</script>

<script>
$(document).ready(function() {
    // Animate cards on load
    $('.analytics-card, .stat-card, .user-selector, .timeline-modern, .video-progress-modern').each(function(index) {
        $(this).css('opacity', '0').css('transform', 'translateY(20px)');
        $(this).delay(index * 100).animate({
            opacity: 1
        }, 500).css('transform', 'translateY(0px)');
    });

    // Smooth scroll for internal links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Enhanced form validation
    $('form').on('submit', function(e) {
        var userSelect = $(this).find('select[name="user_id"]');
        var courseSelect = $(this).find('select[name="course_id"]');

        if (userSelect.length && !userSelect.val()) {
            e.preventDefault();
            userSelect.focus();
            showToast('Please select a user', 'warning');
            return false;
        }

        if (courseSelect.length && !courseSelect.val()) {
            e.preventDefault();
            courseSelect.focus();
            showToast('Please select a course', 'warning');
            return false;
        }
    });

    // Add loading state to buttons
    $('.btn-modern').on('click', function() {
        var $btn = $(this);
        if ($btn.attr('type') === 'submit') {
            $btn.prop('disabled', true);
            var originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Loading...');

            setTimeout(function() {
                $btn.prop('disabled', false);
                $btn.html(originalText);
            }, 3000);
        }
    });

    // Select all videos checkbox (for modal)
    $('#selectAllVideos').on('change', function() {
        $('.video-checkbox:not(:disabled)').prop('checked', $(this).prop('checked'));
    });

    // Update select all checkbox state when individual checkboxes change
    $('.video-checkbox').on('change', function() {
        var allChecked = $('.video-checkbox:not(:disabled)').length === $('.video-checkbox:not(:disabled):checked').length;
        $('#selectAllVideos').prop('checked', allChecked);
    });

    // Toast notification function
    function showToast(message, type = 'info') {
        var toastHtml = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        if (!$('.toast-container').length) {
            $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
        }

        $('.toast-container').append(toastHtml);
        $('.toast').last().toast('show');
    }
});
</script>
