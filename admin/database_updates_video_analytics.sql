-- =====================================================
-- VIDEO ANALYTICS DATABASE UPDATES
-- =====================================================
-- This file contains all necessary database changes for the video analytics system
-- Execute this file in phpMyAdmin SQL tab or MySQL command line
-- =====================================================

-- 1. UPDATE user_video_progress TABLE STRUCTURE
-- =====================================================

-- Add missing columns to user_video_progress table
ALTER TABLE user_video_progress 
ADD COLUMN IF NOT EXISTS progress_percentage DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS watch_time_seconds INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS watch_duration_seconds INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_position_seconds INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_unlocked BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS unlock_date DATE NULL,
ADD COLUMN IF NOT EXISTS completion_date DATETIME NULL,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Add indexes for better performance
ALTER TABLE user_video_progress 
ADD INDEX IF NOT EXISTS idx_user_video (user_id, video_id),
ADD INDEX IF NOT EXISTS idx_completed (is_completed),
ADD INDEX IF NOT EXISTS idx_updated (updated_at),
ADD INDEX IF NOT EXISTS idx_user_completed (user_id, is_completed),
ADD INDEX IF NOT EXISTS idx_video_completed (video_id, is_completed),
ADD INDEX IF NOT EXISTS idx_watch_duration (watch_duration_seconds);

-- 2. CREATE user_activity_log TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS user_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    related_id INT NULL,
    details JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_type_related (user_id, activity_type, related_id),
    INDEX idx_user_activity_date (user_id, activity_type, created_at),
    INDEX idx_related_activity (related_id, activity_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. CREATE user_course_enrollments TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS user_course_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    start_date DATE NOT NULL DEFAULT (CURRENT_DATE),
    end_date DATE NOT NULL DEFAULT (DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_course (user_id, course_id),
    INDEX idx_user_id (user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. INSERT SAMPLE USERS
-- =====================================================

INSERT IGNORE INTO users (id, name, username, email, password, created_at) VALUES
(1, 'John Doe', 'john.doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW()),
(2, 'Jane Smith', 'jane.smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW()),
(3, 'Mike Johnson', 'mike.johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW()),
(4, 'Sarah Wilson', 'sarah.wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW()),
(5, 'David Brown', 'david.brown', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW());

-- 5. INSERT SAMPLE ENROLLMENTS
-- =====================================================

INSERT IGNORE INTO user_course_enrollments (user_id, course_id, status, start_date, end_date) VALUES
(1, 1, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(1, 2, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(2, 1, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(2, 2, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(3, 1, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(3, 3, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(4, 2, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(4, 3, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(5, 1, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
(5, 3, 'active', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY));

-- 6. CLEAR EXISTING SAMPLE DATA (OPTIONAL)
-- =====================================================

DELETE FROM user_activity_log WHERE user_id <= 5;
DELETE FROM user_video_progress WHERE user_id <= 5;

-- 7. INSERT REALISTIC VIDEO PROGRESS DATA
-- =====================================================

INSERT INTO user_video_progress
(user_id, video_id, progress_percentage, watch_time_seconds, completed, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked, unlock_date, completion_date, created_at, updated_at)
VALUES
-- User 1 progress (John Doe)
(1, 1, 85.50, 342, 0, 342, 342, 0, 1, CURDATE(), NULL, NOW(), NOW()),
(1, 2, 100.00, 480, 1, 480, 480, 1, 1, CURDATE(), NOW(), NOW(), NOW()),
(1, 3, 45.25, 181, 0, 181, 181, 0, 1, CURDATE(), NULL, NOW(), NOW()),

-- User 2 progress (Jane Smith)
(2, 1, 100.00, 400, 1, 400, 400, 1, 1, CURDATE(), NOW(), NOW(), NOW()),
(2, 2, 67.80, 271, 0, 271, 271, 0, 1, CURDATE(), NULL, NOW(), NOW()),
(2, 3, 92.30, 369, 0, 369, 369, 0, 1, CURDATE(), NULL, NOW(), NOW()),

-- User 3 progress (Mike Johnson)
(3, 1, 78.90, 315, 0, 315, 315, 0, 1, CURDATE(), NULL, NOW(), NOW()),
(3, 2, 100.00, 450, 1, 450, 450, 1, 1, CURDATE(), NOW(), NOW(), NOW()),
(3, 3, 23.40, 94, 0, 94, 94, 0, 1, CURDATE(), NULL, NOW(), NOW()),

-- User 4 progress (Sarah Wilson)
(4, 1, 56.70, 227, 0, 227, 227, 0, 1, CURDATE(), NULL, NOW(), NOW()),
(4, 2, 89.20, 357, 0, 357, 357, 0, 1, CURDATE(), NULL, NOW(), NOW()),
(4, 3, 100.00, 420, 1, 420, 420, 1, 1, CURDATE(), NOW(), NOW(), NOW()),

-- User 5 progress (David Brown)
(5, 1, 100.00, 390, 1, 390, 390, 1, 1, CURDATE(), NOW(), NOW(), NOW()),
(5, 2, 34.60, 138, 0, 138, 138, 0, 1, CURDATE(), NULL, NOW(), NOW()),
(5, 3, 71.80, 287, 0, 287, 287, 0, 1, CURDATE(), NULL, NOW(), NOW());

-- 8. INSERT ACTIVITY LOG DATA
-- =====================================================

INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at) VALUES
-- User 1 activities (John Doe)
(1, 'video_progress', 1, '{"action":"progress","watch_duration":342,"last_position":342,"is_completed":false,"timestamp":"2024-12-19 10:30:00"}', '2024-12-19 10:30:00'),
(1, 'video_progress', 2, '{"action":"complete","watch_duration":480,"last_position":480,"is_completed":true,"timestamp":"2024-12-19 11:15:00"}', '2024-12-19 11:15:00'),
(1, 'video_progress', 3, '{"action":"progress","watch_duration":181,"last_position":181,"is_completed":false,"timestamp":"2024-12-19 12:00:00"}', '2024-12-19 12:00:00'),

-- User 2 activities (Jane Smith)
(2, 'video_progress', 1, '{"action":"complete","watch_duration":400,"last_position":400,"is_completed":true,"timestamp":"2024-12-19 09:45:00"}', '2024-12-19 09:45:00'),
(2, 'video_progress', 2, '{"action":"progress","watch_duration":271,"last_position":271,"is_completed":false,"timestamp":"2024-12-19 10:20:00"}', '2024-12-19 10:20:00'),
(2, 'video_progress', 3, '{"action":"progress","watch_duration":369,"last_position":369,"is_completed":false,"timestamp":"2024-12-19 11:30:00"}', '2024-12-19 11:30:00'),

-- User 3 activities (Mike Johnson)
(3, 'video_progress', 1, '{"action":"progress","watch_duration":315,"last_position":315,"is_completed":false,"timestamp":"2024-12-19 08:30:00"}', '2024-12-19 08:30:00'),
(3, 'video_progress', 2, '{"action":"complete","watch_duration":450,"last_position":450,"is_completed":true,"timestamp":"2024-12-19 09:15:00"}', '2024-12-19 09:15:00'),
(3, 'video_progress', 3, '{"action":"progress","watch_duration":94,"last_position":94,"is_completed":false,"timestamp":"2024-12-19 10:45:00"}', '2024-12-19 10:45:00'),

-- User 4 activities (Sarah Wilson)
(4, 'video_progress', 1, '{"action":"progress","watch_duration":227,"last_position":227,"is_completed":false,"timestamp":"2024-12-19 07:20:00"}', '2024-12-19 07:20:00'),
(4, 'video_progress', 2, '{"action":"progress","watch_duration":357,"last_position":357,"is_completed":false,"timestamp":"2024-12-19 08:45:00"}', '2024-12-19 08:45:00'),
(4, 'video_progress', 3, '{"action":"complete","watch_duration":420,"last_position":420,"is_completed":true,"timestamp":"2024-12-19 09:30:00"}', '2024-12-19 09:30:00'),

-- User 5 activities (David Brown)
(5, 'video_progress', 1, '{"action":"complete","watch_duration":390,"last_position":390,"is_completed":true,"timestamp":"2024-12-19 06:45:00"}', '2024-12-19 06:45:00'),
(5, 'video_progress', 2, '{"action":"progress","watch_duration":138,"last_position":138,"is_completed":false,"timestamp":"2024-12-19 07:30:00"}', '2024-12-19 07:30:00'),
(5, 'video_progress', 3, '{"action":"progress","watch_duration":287,"last_position":287,"is_completed":false,"timestamp":"2024-12-19 08:15:00"}', '2024-12-19 08:15:00');

-- 9. VERIFICATION QUERIES
-- =====================================================

-- Check user progress summary
SELECT
    u.name as user_name,
    c.title as course_title,
    COUNT(uvp.id) as videos_watched,
    SUM(uvp.watch_duration_seconds) as total_watch_time,
    COUNT(CASE WHEN uvp.is_completed = 1 THEN 1 END) as completed_videos,
    ROUND(AVG(uvp.progress_percentage), 2) as avg_progress
FROM users u
JOIN user_course_enrollments uce ON u.id = uce.user_id
JOIN courses c ON uce.course_id = c.id
LEFT JOIN user_video_progress uvp ON u.id = uvp.user_id
WHERE u.id <= 5
GROUP BY u.id, c.id
ORDER BY u.name, c.title;

-- Check recent activity
SELECT
    u.name,
    ual.activity_type,
    cv.title as video_title,
    JSON_EXTRACT(ual.details, '$.action') as action,
    JSON_EXTRACT(ual.details, '$.watch_duration') as watch_duration,
    ual.created_at
FROM user_activity_log ual
JOIN users u ON ual.user_id = u.id
JOIN course_videos cv ON ual.related_id = cv.id
WHERE ual.activity_type = 'video_progress'
ORDER BY ual.created_at DESC
LIMIT 15;

-- Check table counts
SELECT
    'users' as table_name,
    COUNT(*) as record_count
FROM users WHERE id <= 5
UNION ALL
SELECT
    'user_video_progress' as table_name,
    COUNT(*) as record_count
FROM user_video_progress WHERE user_id <= 5
UNION ALL
SELECT
    'user_activity_log' as table_name,
    COUNT(*) as record_count
FROM user_activity_log WHERE user_id <= 5 AND activity_type = 'video_progress'
UNION ALL
SELECT
    'user_course_enrollments' as table_name,
    COUNT(*) as record_count
FROM user_course_enrollments WHERE user_id <= 5;

-- =====================================================
-- EXECUTION COMPLETE
-- =====================================================
-- After running this file, your video analytics system should be fully functional
-- Test the analytics page at: admin/video_analytics.php?user_id=1&course_id=1
-- =====================================================
