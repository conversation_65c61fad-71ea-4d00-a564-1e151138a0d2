<?php
/**
 * Simulate Video Progress Data
 * This script creates sample video progress data for testing analytics
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

echo "<h2>Simulate Video Progress Data</h2>\n";
echo "<pre>\n";

try {
    $testUserId = 27;
    $testCourseId = 2;
    
    echo "Creating sample video progress data for User ID: $testUserId, Course ID: $testCourseId\n\n";

    // 1. Get course videos
    echo "1. Getting course videos...\n";
    $videosStmt = $conn->prepare("SELECT id, title, week_number FROM course_videos WHERE course_id = ? ORDER BY week_number, sequence_number LIMIT 5");
    $videosStmt->bind_param("i", $testCourseId);
    $videosStmt->execute();
    $videosResult = $videosStmt->get_result();
    $videos = [];
    while ($video = $videosResult->fetch_assoc()) {
        $videos[] = $video;
    }
    
    if (empty($videos)) {
        echo "   ❌ No videos found for course $testCourseId\n";
        exit;
    }
    
    echo "   Found " . count($videos) . " videos\n";
    foreach ($videos as $video) {
        echo "     - Video {$video['id']}: {$video['title']}\n";
    }

    // 2. Create sample progress data
    echo "\n2. Creating sample progress data...\n";
    
    $sampleProgressData = [
        ['video_id' => $videos[0]['id'], 'watch_duration' => 180, 'last_position' => 180, 'completed' => true],
        ['video_id' => $videos[1]['id'], 'watch_duration' => 120, 'last_position' => 120, 'completed' => false],
        ['video_id' => $videos[2]['id'], 'watch_duration' => 300, 'last_position' => 300, 'completed' => true],
    ];

    foreach ($sampleProgressData as $data) {
        if (!isset($videos[array_search($data['video_id'], array_column($videos, 'id'))])) {
            continue;
        }

        // Insert/Update user_video_progress
        $progressStmt = $conn->prepare("
            INSERT INTO user_video_progress 
            (user_id, video_id, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked, unlock_date, updated_at)
            VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
            ON DUPLICATE KEY UPDATE
            watch_duration_seconds = VALUES(watch_duration_seconds),
            last_position_seconds = VALUES(last_position_seconds),
            is_completed = VALUES(is_completed),
            updated_at = NOW()
        ");
        
        $progressStmt->bind_param("iiiii", 
            $testUserId, 
            $data['video_id'], 
            $data['watch_duration'], 
            $data['last_position'], 
            $data['completed'] ? 1 : 0
        );
        
        if ($progressStmt->execute()) {
            echo "   ✅ Updated progress for video {$data['video_id']}: {$data['watch_duration']}s watched, completed: " . ($data['completed'] ? 'Yes' : 'No') . "\n";
        } else {
            echo "   ❌ Failed to update progress for video {$data['video_id']}\n";
        }

        // Insert activity log
        $activityStmt = $conn->prepare("
            INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at)
            VALUES (?, 'video_progress', ?, ?, NOW())
        ");
        
        $details = json_encode([
            'action' => $data['completed'] ? 'complete' : 'progress_update',
            'watch_duration' => $data['watch_duration'],
            'last_position' => $data['last_position'],
            'is_completed' => $data['completed'],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $activityStmt->bind_param("iis", $testUserId, $data['video_id'], $details);
        
        if ($activityStmt->execute()) {
            echo "   ✅ Created activity log for video {$data['video_id']}\n";
        } else {
            echo "   ❌ Failed to create activity log for video {$data['video_id']}\n";
        }
    }

    // 3. Verify the data
    echo "\n3. Verifying created data...\n";
    
    // Check progress records
    $progressCheckStmt = $conn->prepare("
        SELECT video_id, watch_duration_seconds, is_completed 
        FROM user_video_progress 
        WHERE user_id = ? 
        ORDER BY video_id
    ");
    $progressCheckStmt->bind_param("i", $testUserId);
    $progressCheckStmt->execute();
    $progressCheckResult = $progressCheckStmt->get_result();
    
    echo "   Progress records:\n";
    while ($progress = $progressCheckResult->fetch_assoc()) {
        echo "     - Video {$progress['video_id']}: {$progress['watch_duration_seconds']}s, completed: {$progress['is_completed']}\n";
    }
    
    // Check activity records
    $activityCheckStmt = $conn->prepare("
        SELECT related_id, details 
        FROM user_activity_log 
        WHERE user_id = ? AND activity_type = 'video_progress'
        ORDER BY related_id
    ");
    $activityCheckStmt->bind_param("i", $testUserId);
    $activityCheckStmt->execute();
    $activityCheckResult = $activityCheckStmt->get_result();
    
    echo "\n   Activity records:\n";
    while ($activity = $activityCheckResult->fetch_assoc()) {
        $details = json_decode($activity['details'], true);
        echo "     - Video {$activity['related_id']}: {$details['watch_duration']}s, action: {$details['action']}\n";
    }

    // 4. Test analytics query
    echo "\n4. Testing analytics query...\n";
    
    $analyticsStmt = $conn->prepare("
        SELECT
            COUNT(DISTINCT al.id) as total_views,
            SUM(CASE
                WHEN al.details LIKE '%watch_duration%' THEN
                    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"watch_duration\":', -1), ',', 1) AS UNSIGNED)
                ELSE 0
            END) as total_watch_duration,
            COUNT(DISTINCT CASE
                WHEN al.details LIKE '%\"is_completed\":true%' OR al.details LIKE '%\"is_completed\":1%'
                THEN al.related_id
            END) as completed_videos
        FROM user_activity_log al
        JOIN course_videos cv ON al.related_id = cv.id
        WHERE al.user_id = ? AND cv.course_id = ? AND al.activity_type = 'video_progress'
    ");
    
    $analyticsStmt->bind_param("ii", $testUserId, $testCourseId);
    $analyticsStmt->execute();
    $analyticsResult = $analyticsStmt->get_result();
    $analytics = $analyticsResult->fetch_assoc();
    
    echo "   Analytics results:\n";
    echo "     - Total views: {$analytics['total_views']}\n";
    echo "     - Total watch duration: {$analytics['total_watch_duration']} seconds\n";
    echo "     - Completed videos: {$analytics['completed_videos']}\n";

    echo "\n✅ Sample data created successfully!\n";
    echo "\nNow test the analytics page: https://mycloudforge.com/admin/video_analytics.php?user_id=$testUserId&course_id=$testCourseId\n";

} catch (Exception $e) {
    echo "\n❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>

<style>
body { font-family: monospace; background: #f5f5f5; padding: 20px; }
pre { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
h2 { color: #333; }
</style>
