<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

echo "🔧 Fixing Video Analytics Database Issues...\n\n";

try {
    // 1. Create user_activity_log table
    echo "1. Checking user_activity_log table...\n";
    $result = $conn->query("SHOW TABLES LIKE 'user_activity_log'");
    
    if ($result->num_rows == 0) {
        echo "   ❌ Table missing - Creating user_activity_log table...\n";
        $createActivityLogSql = "
        CREATE TABLE user_activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            activity_type VARCHAR(50) NOT NULL,
            related_id INT NULL,
            details JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_activity_type (activity_type),
            INDEX idx_created_at (created_at),
            INDEX idx_user_type_related (user_id, activity_type, related_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->query($createActivityLogSql);
        echo "   ✅ user_activity_log table created successfully\n";
    } else {
        echo "   ✅ user_activity_log table exists\n";
    }

    // 2. Check and update user_video_progress table
    echo "\n2. Checking user_video_progress table structure...\n";
    $result = $conn->query("SHOW COLUMNS FROM user_video_progress");
    $existingColumns = [];
    while ($row = $result->fetch_assoc()) {
        $existingColumns[] = $row['Field'];
    }
    
    // Add missing columns if needed
    $requiredColumns = [
        'watch_duration_seconds' => 'INT DEFAULT 0',
        'last_position_seconds' => 'INT DEFAULT 0',
        'is_completed' => 'BOOLEAN DEFAULT FALSE',
        'is_unlocked' => 'BOOLEAN DEFAULT TRUE',
        'unlock_date' => 'DATE NULL',
        'completion_date' => 'DATETIME NULL'
    ];
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $existingColumns)) {
            echo "   Adding missing column: $column\n";
            $sql = "ALTER TABLE user_video_progress ADD COLUMN $column $definition";
            $conn->query($sql);
        }
    }
    echo "   ✅ user_video_progress table structure verified\n";

    // 3. Check if we have sample data
    echo "\n3. Checking sample data...\n";
    $userCount = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $progressCount = $conn->query("SELECT COUNT(*) as count FROM user_video_progress")->fetch_assoc()['count'];
    $activityCount = $conn->query("SELECT COUNT(*) as count FROM user_activity_log WHERE activity_type = 'video_progress'")->fetch_assoc()['count'];
    
    echo "   Users: $userCount\n";
    echo "   Progress records: $progressCount\n";
    echo "   Activity records: $activityCount\n";

    // 4. Create sample data if missing
    if ($userCount == 0 || $progressCount == 0 || $activityCount == 0) {
        echo "\n4. Creating sample data...\n";
        
        // Create sample users if none exist
        if ($userCount == 0) {
            echo "   Creating sample users...\n";
            $sampleUsers = [
                ['John Doe', 'john.doe', '<EMAIL>'],
                ['Jane Smith', 'jane.smith', '<EMAIL>'],
                ['Mike Johnson', 'mike.johnson', '<EMAIL>'],
                ['Sarah Wilson', 'sarah.wilson', '<EMAIL>'],
                ['David Brown', 'david.brown', '<EMAIL>']
            ];
            
            foreach ($sampleUsers as $index => $user) {
                $userId = $index + 1;
                $stmt = $conn->prepare("INSERT INTO users (id, name, username, email, password, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
                $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
                $stmt->bind_param("issss", $userId, $user[0], $user[1], $user[2], $hashedPassword);
                $stmt->execute();
            }
            echo "   ✅ Created 5 sample users\n";
        }

        // Create enrollments if needed
        $enrollmentCount = $conn->query("SELECT COUNT(*) as count FROM user_course_enrollments")->fetch_assoc()['count'];
        if ($enrollmentCount == 0) {
            echo "   Creating sample enrollments...\n";
            for ($userId = 1; $userId <= 5; $userId++) {
                for ($courseId = 1; $courseId <= 3; $courseId++) {
                    $stmt = $conn->prepare("INSERT IGNORE INTO user_course_enrollments (user_id, course_id) VALUES (?, ?)");
                    $stmt->bind_param("ii", $userId, $courseId);
                    $stmt->execute();
                }
            }
            echo "   ✅ Created sample enrollments\n";
        }

        // Create video progress and activity data
        echo "   Creating video progress and activity data...\n";
        for ($userId = 1; $userId <= 5; $userId++) {
            for ($videoId = 1; $videoId <= 3; $videoId++) {
                $watchDuration = rand(60, 600);
                $lastPosition = $watchDuration;
                $isCompleted = rand(0, 1);
                $completionDate = $isCompleted ? date('Y-m-d H:i:s') : null;
                $progressPercentage = round(($watchDuration / 600) * 100, 2);
                
                // Insert/update progress record
                $stmt = $conn->prepare("INSERT INTO user_video_progress (user_id, video_id, progress_percentage, watch_time_seconds, completed, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked, unlock_date, completion_date, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, CURDATE(), ?, NOW(), NOW()) ON DUPLICATE KEY UPDATE watch_duration_seconds = VALUES(watch_duration_seconds), is_completed = VALUES(is_completed), completion_date = VALUES(completion_date)");
                $stmt->bind_param("iidiiiiis", $userId, $videoId, $progressPercentage, $watchDuration, $isCompleted, $watchDuration, $lastPosition, $isCompleted, $completionDate);
                $stmt->execute();
                
                // Insert activity log
                $details = json_encode([
                    'action' => $isCompleted ? 'complete' : 'progress',
                    'watch_duration' => $watchDuration,
                    'last_position' => $lastPosition,
                    'is_completed' => $isCompleted,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
                
                $stmt = $conn->prepare("INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at) VALUES (?, 'video_progress', ?, ?, NOW())");
                $stmt->bind_param("iis", $userId, $videoId, $details);
                $stmt->execute();
            }
        }
        echo "   ✅ Created video progress and activity data\n";
    }

    // 5. Final verification
    echo "\n5. Final verification...\n";
    $finalUserCount = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $finalProgressCount = $conn->query("SELECT COUNT(*) as count FROM user_video_progress")->fetch_assoc()['count'];
    $finalActivityCount = $conn->query("SELECT COUNT(*) as count FROM user_activity_log WHERE activity_type = 'video_progress'")->fetch_assoc()['count'];
    
    echo "   ✅ Users: $finalUserCount\n";
    echo "   ✅ Progress records: $finalProgressCount\n";
    echo "   ✅ Activity records: $finalActivityCount\n";

    echo "\n🎉 Database fix completed successfully!\n";
    echo "\nYou can now test the analytics with:\n";
    echo "- User ID: 1 (John Doe) with Course ID: 1\n";
    echo "- User ID: 2 (Jane Smith) with Course ID: 2\n";
    echo "- Or any combination of users 1-5 and courses 1-3\n";

} catch (Exception $e) {
    echo "\n❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
