<?php
/**
 * Add Test Video Data
 * Simple script to add test video progress data
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

$testUserId = 27;
$testCourseId = 2;

echo "<h2>Adding Test Video Data</h2>\n";
echo "<pre>\n";

try {
    // Get first video from the course
    $videoStmt = $conn->prepare("SELECT id, title FROM course_videos WHERE course_id = ? LIMIT 1");
    $videoStmt->bind_param("i", $testCourseId);
    $videoStmt->execute();
    $videoResult = $videoStmt->get_result();
    $video = $videoResult->fetch_assoc();
    
    if (!$video) {
        echo "No videos found for course $testCourseId\n";
        exit;
    }
    
    $videoId = $video['id'];
    echo "Using video: {$video['title']} (ID: $videoId)\n\n";
    
    // 1. Add progress record
    echo "1. Adding progress record...\n";
    $progressSql = "
        INSERT INTO user_video_progress 
        (user_id, video_id, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked, unlock_date, created_at, updated_at)
        VALUES (?, ?, 180, 180, 1, 1, NOW(), NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        watch_duration_seconds = 180,
        last_position_seconds = 180,
        is_completed = 1,
        updated_at = NOW()
    ";
    
    $progressStmt = $conn->prepare($progressSql);
    $progressStmt->bind_param("ii", $testUserId, $videoId);
    
    if ($progressStmt->execute()) {
        echo "   ✅ Progress record added/updated\n";
    } else {
        echo "   ❌ Failed to add progress record: " . $conn->error . "\n";
    }
    
    // 2. Add activity log
    echo "\n2. Adding activity log...\n";
    $activitySql = "
        INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at)
        VALUES (?, 'video_progress', ?, ?, NOW())
    ";
    
    $details = json_encode([
        'action' => 'complete',
        'watch_duration' => 180,
        'last_position' => 180,
        'is_completed' => true,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    $activityStmt = $conn->prepare($activitySql);
    $activityStmt->bind_param("iis", $testUserId, $videoId, $details);
    
    if ($activityStmt->execute()) {
        echo "   ✅ Activity log added\n";
    } else {
        echo "   ❌ Failed to add activity log: " . $conn->error . "\n";
    }
    
    // 3. Verify data
    echo "\n3. Verifying data...\n";
    
    // Check progress
    $checkProgressStmt = $conn->prepare("SELECT * FROM user_video_progress WHERE user_id = ? AND video_id = ?");
    $checkProgressStmt->bind_param("ii", $testUserId, $videoId);
    $checkProgressStmt->execute();
    $progressData = $checkProgressStmt->get_result()->fetch_assoc();
    
    if ($progressData) {
        echo "   Progress: {$progressData['watch_duration_seconds']}s watched, completed: {$progressData['is_completed']}\n";
    } else {
        echo "   ❌ No progress data found\n";
    }
    
    // Check activity
    $checkActivityStmt = $conn->prepare("SELECT details FROM user_activity_log WHERE user_id = ? AND related_id = ? AND activity_type = 'video_progress' ORDER BY created_at DESC LIMIT 1");
    $checkActivityStmt->bind_param("ii", $testUserId, $videoId);
    $checkActivityStmt->execute();
    $activityData = $checkActivityStmt->get_result()->fetch_assoc();
    
    if ($activityData) {
        $details = json_decode($activityData['details'], true);
        echo "   Activity: {$details['action']}, {$details['watch_duration']}s\n";
    } else {
        echo "   ❌ No activity data found\n";
    }
    
    echo "\n✅ Test data added successfully!\n";
    echo "\nNow check: https://mycloudforge.com/admin/video_analytics.php?user_id=$testUserId&course_id=$testCourseId\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "</pre>\n";
?>

<style>
body { font-family: monospace; background: #f5f5f5; padding: 20px; }
pre { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
h2 { color: #333; }
</style>
