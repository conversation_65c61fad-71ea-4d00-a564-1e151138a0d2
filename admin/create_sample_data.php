<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

echo "Creating sample data for video analytics...\n";

try {
    // Check if user_course_enrollments table exists
    $result = $conn->query("SHOW TABLES LIKE 'user_course_enrollments'");
    if ($result->num_rows == 0) {
        echo "Creating user_course_enrollments table...\n";
        $sql = "
        CREATE TABLE user_course_enrollments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            status VARCHAR(20) DEFAULT 'active',
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_course (user_id, course_id)
        )";
        $conn->query($sql);
        echo "Table created.\n";
    }

    // Clear existing sample data
    $conn->query("DELETE FROM user_activity_log WHERE user_id <= 5");
    $conn->query("DELETE FROM user_video_progress WHERE user_id <= 5");
    $conn->query("DELETE FROM user_course_enrollments WHERE user_id <= 5");
    $conn->query("DELETE FROM users WHERE id <= 5");

    // Create sample users
    echo "Creating sample users...\n";
    $sampleUsers = [
        ['John Doe', 'john.doe', '<EMAIL>'],
        ['Jane Smith', 'jane.smith', '<EMAIL>'],
        ['Mike Johnson', 'mike.johnson', '<EMAIL>'],
        ['Sarah Wilson', 'sarah.wilson', '<EMAIL>'],
        ['David Brown', 'david.brown', '<EMAIL>']
    ];

    foreach ($sampleUsers as $index => $user) {
        $userId = $index + 1;
        $stmt = $conn->prepare("INSERT INTO users (id, name, username, email, password, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
        $stmt->bind_param("issss", $userId, $user[0], $user[1], $user[2], $hashedPassword);
        $stmt->execute();
        echo "Created user: {$user[0]} (ID: $userId)\n";
    }

    // Create sample enrollments
    echo "Creating sample enrollments...\n";
    for ($userId = 1; $userId <= 5; $userId++) {
        for ($courseId = 1; $courseId <= 3; $courseId++) {
            // Try to insert with minimal fields first
            try {
                $stmt = $conn->prepare("INSERT INTO user_course_enrollments (user_id, course_id) VALUES (?, ?)");
                $stmt->bind_param("ii", $userId, $courseId);
                $stmt->execute();
                echo "Enrolled user $userId in course $courseId\n";
            } catch (Exception $e) {
                echo "Enrollment error for user $userId, course $courseId: " . $e->getMessage() . "\n";
            }
        }
    }

    // Create sample video progress
    echo "Creating sample video progress...\n";
    for ($userId = 1; $userId <= 5; $userId++) {
        for ($videoId = 1; $videoId <= 3; $videoId++) {
            $watchDuration = rand(60, 600); // 1-10 minutes
            $lastPosition = $watchDuration;
            $isCompleted = rand(0, 1);
            $completionDate = $isCompleted ? date('Y-m-d H:i:s') : null;
            $progressPercentage = round(($watchDuration / 600) * 100, 2); // Assume 10 min max video

            // Insert progress record with all available columns
            $stmt = $conn->prepare("INSERT INTO user_video_progress (user_id, video_id, progress_percentage, watch_time_seconds, completed, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked, unlock_date, completion_date, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, CURDATE(), ?, NOW(), NOW())");
            $stmt->bind_param("iidiiiiis", $userId, $videoId, $progressPercentage, $watchDuration, $isCompleted, $watchDuration, $lastPosition, $isCompleted, $completionDate);
            $stmt->execute();

            // Insert activity log
            $details = json_encode([
                'action' => $isCompleted ? 'complete' : 'progress',
                'watch_duration' => $watchDuration,
                'last_position' => $lastPosition,
                'is_completed' => $isCompleted,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            $stmt = $conn->prepare("INSERT INTO user_activity_log (user_id, activity_type, related_id, details, created_at) VALUES (?, 'video_progress', ?, ?, NOW())");
            $stmt->bind_param("iis", $userId, $videoId, $details);
            $stmt->execute();

            echo "Created progress for user $userId, video $videoId (watched: {$watchDuration}s, completed: " . ($isCompleted ? 'yes' : 'no') . ")\n";
        }
    }

    echo "\n✅ Sample data creation completed successfully!\n";
    echo "\nYou can now test the analytics with:\n";
    echo "- User ID: 1 (John Doe) with Course ID: 1 (Beginner Fitness Journey)\n";
    echo "- User ID: 2 (Jane Smith) with Course ID: 2 (Intermediate Strength Training)\n";
    echo "- Or any combination of users 1-5 and courses 1-3\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
