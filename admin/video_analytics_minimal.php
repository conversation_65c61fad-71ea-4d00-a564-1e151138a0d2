<?php
// Minimal video analytics page to test what's causing the 500 error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Video Analytics Test</title></head><body>";
echo "<h1>Video Analytics Minimal Test</h1>";

try {
    echo "<p>1. Testing includes...</p>";
    
    require_once 'includes/config.php';
    echo "<p>✅ Config loaded</p>";
    
    require_once 'includes/auth.php';
    echo "<p>✅ Auth loaded</p>";
    
    require_once 'includes/database.php';
    echo "<p>✅ Database loaded</p>";
    
    require_once 'includes/utilities.php';
    echo "<p>✅ Utilities loaded</p>";

    echo "<p>2. Testing auth...</p>";
    $auth = new Auth();
    echo "<p>✅ Auth object created</p>";

    // Skip auth check for testing
    echo "<p>3. Testing database...</p>";
    $db = new Database();
    $conn = $db->getConnection();
    echo "<p>✅ Database connected</p>";

    echo "<p>4. Testing queries...</p>";
    $userId = 27;
    $courseId = 2;

    // Get users
    $usersQuery = "SELECT id, name, username FROM users ORDER BY name LIMIT 10";
    $usersStmt = $conn->prepare($usersQuery);
    $usersStmt->execute();
    $usersResult = $usersStmt->get_result();
    $users = [];
    while ($user = $usersResult->fetch_assoc()) {
        $users[] = $user;
    }
    echo "<p>✅ Found " . count($users) . " users</p>";

    // Get courses
    $coursesQuery = "SELECT id, title FROM courses ORDER BY title LIMIT 10";
    $coursesStmt = $conn->prepare($coursesQuery);
    $coursesStmt->execute();
    $coursesResult = $coursesStmt->get_result();
    $courses = [];
    while ($course = $coursesResult->fetch_assoc()) {
        $courses[] = $course;
    }
    echo "<p>✅ Found " . count($courses) . " courses</p>";

    if ($courseId > 0 && $userId > 0) {
        echo "<p>5. Testing specific user/course data...</p>";
        
        // Get course details
        $courseQuery = "SELECT * FROM courses WHERE id = ?";
        $courseStmt = $conn->prepare($courseQuery);
        $courseStmt->bind_param("i", $courseId);
        $courseStmt->execute();
        $courseResult = $courseStmt->get_result();
        $selectedCourse = $courseResult->fetch_assoc();
        
        if ($selectedCourse) {
            echo "<p>✅ Course found: " . htmlspecialchars($selectedCourse['title']) . "</p>";
        } else {
            echo "<p>❌ Course not found</p>";
        }

        // Get user details
        $userQuery = "SELECT * FROM users WHERE id = ?";
        $userStmt = $conn->prepare($userQuery);
        $userStmt->bind_param("i", $userId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();
        $selectedUser = $userResult->fetch_assoc();
        
        if ($selectedUser) {
            echo "<p>✅ User found: " . htmlspecialchars($selectedUser['name']) . "</p>";
        } else {
            echo "<p>❌ User not found</p>";
        }

        // Get videos
        echo "<p>6. Testing video query...</p>";
        $videosQuery = "SELECT v.id, v.title, v.week_number
                       FROM course_videos v
                       WHERE v.course_id = ?
                       ORDER BY v.week_number, v.sequence_number";
        $videosStmt = $conn->prepare($videosQuery);
        $videosStmt->bind_param("i", $courseId);
        $videosStmt->execute();
        $videosResult = $videosStmt->get_result();
        $videos = [];
        while ($video = $videosResult->fetch_assoc()) {
            $videos[] = $video;
        }
        echo "<p>✅ Found " . count($videos) . " videos</p>";

        // Test analytics query
        echo "<p>7. Testing analytics query...</p>";
        $analytics = ['total_views' => 0, 'total_watch_duration' => 0, 'completed_videos' => 0, 'last_activity' => null];
        
        try {
            $progressQuery = "SELECT
                             COUNT(DISTINCT p.video_id) as total_views,
                             SUM(p.watch_duration_seconds) as total_watch_duration,
                             COUNT(DISTINCT CASE WHEN p.is_completed = 1 THEN p.video_id END) as completed_videos,
                             MAX(p.updated_at) as last_activity
                             FROM user_video_progress p
                             JOIN course_videos cv ON p.video_id = cv.id
                             WHERE p.user_id = ? AND cv.course_id = ? AND p.watch_duration_seconds > 0";
            $progressStmt = $conn->prepare($progressQuery);
            $progressStmt->bind_param("ii", $userId, $courseId);
            $progressStmt->execute();
            $progressResult = $progressStmt->get_result();
            $progressAnalytics = $progressResult->fetch_assoc();
            
            if ($progressAnalytics && $progressAnalytics['total_views'] > 0) {
                $analytics = $progressAnalytics;
                echo "<p>✅ Analytics data found: {$analytics['total_views']} views, {$analytics['total_watch_duration']} seconds watched</p>";
            } else {
                echo "<p>⚠️ No analytics data found</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Analytics query error: " . $e->getMessage() . "</p>";
        }

        echo "<h2>Analytics Summary</h2>";
        echo "<ul>";
        echo "<li>User: " . htmlspecialchars($selectedUser['name'] ?? 'Unknown') . "</li>";
        echo "<li>Course: " . htmlspecialchars($selectedCourse['title'] ?? 'Unknown') . "</li>";
        echo "<li>Videos: " . count($videos) . "</li>";
        echo "<li>Total Views: " . ($analytics['total_views'] ?? 0) . "</li>";
        echo "<li>Watch Duration: " . ($analytics['total_watch_duration'] ?? 0) . " seconds</li>";
        echo "<li>Completed: " . ($analytics['completed_videos'] ?? 0) . "</li>";
        echo "</ul>";
    }

    echo "<p>✅ All tests completed successfully!</p>";
    echo "<p><a href='video_analytics.php?user_id=27&course_id=2'>Try full analytics page</a></p>";

} catch (Exception $e) {
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<p>❌ Fatal Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
}

echo "</body></html>";
?>
