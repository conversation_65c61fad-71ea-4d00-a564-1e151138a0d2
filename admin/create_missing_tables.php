<?php
/**
 * Create Missing Tables for Video Analytics
 */

require_once 'includes/config.php';
require_once 'includes/database.php';

$db = new Database();
$conn = $db->getConnection();

echo "<h2>Creating Missing Tables</h2>\n";
echo "<pre>\n";

try {
    // 1. Create user_activity_log table
    echo "1. Creating user_activity_log table...\n";
    
    $createActivityLogTable = "
    CREATE TABLE IF NOT EXISTS user_activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        activity_type VARCHAR(50) NOT NULL,
        related_id INT NULL,
        details JSON NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_activity_type (activity_type),
        INDEX idx_created_at (created_at),
        INDEX idx_user_activity_type (user_id, activity_type),
        INDEX idx_user_type_related (user_id, activity_type, related_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($createActivityLogTable)) {
        echo "   ✅ user_activity_log table created successfully\n";
    } else {
        echo "   ❌ Error creating user_activity_log table: " . $conn->error . "\n";
    }

    // 2. Create video_access_logs table if it doesn't exist
    echo "\n2. Creating video_access_logs table...\n";
    
    $createVideoAccessTable = "
    CREATE TABLE IF NOT EXISTS video_access_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        vimeo_id VARCHAR(50),
        video_id INT NOT NULL,
        user_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        timestamp TIMESTAMP NULL,
        app_domain VARCHAR(255),
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_video_id (video_id),
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_timestamp (timestamp)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($createVideoAccessTable)) {
        echo "   ✅ video_access_logs table created successfully\n";
    } else {
        echo "   ❌ Error creating video_access_logs table: " . $conn->error . "\n";
    }

    // 3. Verify user_video_progress table exists and has correct structure
    echo "\n3. Checking user_video_progress table...\n";
    
    $result = $conn->query("SHOW TABLES LIKE 'user_video_progress'");
    if ($result->num_rows > 0) {
        echo "   ✅ user_video_progress table exists\n";
        
        // Check columns
        $result = $conn->query("SHOW COLUMNS FROM user_video_progress");
        $columns = [];
        while ($row = $result->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        echo "   Columns: " . implode(', ', $columns) . "\n";
        
        // Add missing columns if needed
        $requiredColumns = [
            'watch_duration_seconds' => 'INT DEFAULT 0',
            'last_position_seconds' => 'INT DEFAULT 0',
            'is_completed' => 'BOOLEAN DEFAULT FALSE',
            'is_unlocked' => 'BOOLEAN DEFAULT TRUE'
        ];
        
        foreach ($requiredColumns as $column => $definition) {
            if (!in_array($column, $columns)) {
                echo "   Adding missing column: $column\n";
                $sql = "ALTER TABLE user_video_progress ADD COLUMN $column $definition";
                if ($conn->query($sql)) {
                    echo "   ✅ Added $column column\n";
                } else {
                    echo "   ❌ Error adding $column: " . $conn->error . "\n";
                }
            }
        }
    } else {
        echo "   Creating user_video_progress table...\n";
        $createProgressTable = "
        CREATE TABLE user_video_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            video_id INT NOT NULL,
            watch_duration_seconds INT DEFAULT 0,
            last_position_seconds INT DEFAULT 0,
            is_completed BOOLEAN DEFAULT FALSE,
            is_unlocked BOOLEAN DEFAULT TRUE,
            unlock_date DATE NULL,
            completion_date DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_video (user_id, video_id),
            INDEX idx_user_id (user_id),
            INDEX idx_video_id (video_id),
            INDEX idx_completed (is_completed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($createProgressTable)) {
            echo "   ✅ user_video_progress table created successfully\n";
        } else {
            echo "   ❌ Error creating user_video_progress table: " . $conn->error . "\n";
        }
    }

    // 4. Add some test data
    echo "\n4. Adding test data...\n";
    
    $testUserId = 27;
    $testCourseId = 2;
    
    // Get a video from the course
    $videoStmt = $conn->prepare("SELECT id, title FROM course_videos WHERE course_id = ? LIMIT 1");
    $videoStmt->bind_param("i", $testCourseId);
    $videoStmt->execute();
    $video = $videoStmt->get_result()->fetch_assoc();
    
    if ($video) {
        $videoId = $video['id'];
        echo "   Using video: {$video['title']} (ID: $videoId)\n";
        
        // Add progress record
        $progressSql = "
        INSERT INTO user_video_progress 
        (user_id, video_id, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked)
        VALUES (?, ?, 180, 180, 1, 1)
        ON DUPLICATE KEY UPDATE
        watch_duration_seconds = 180,
        last_position_seconds = 180,
        is_completed = 1,
        updated_at = NOW()
        ";
        
        $progressStmt = $conn->prepare($progressSql);
        $progressStmt->bind_param("ii", $testUserId, $videoId);
        
        if ($progressStmt->execute()) {
            echo "   ✅ Test progress record added\n";
        } else {
            echo "   ❌ Error adding progress: " . $conn->error . "\n";
        }
        
        // Add activity log
        $activitySql = "
        INSERT INTO user_activity_log (user_id, activity_type, related_id, details)
        VALUES (?, 'video_progress', ?, ?)
        ";
        
        $details = json_encode([
            'action' => 'complete',
            'watch_duration' => 180,
            'last_position' => 180,
            'is_completed' => true,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $activityStmt = $conn->prepare($activitySql);
        $activityStmt->bind_param("iis", $testUserId, $videoId, $details);
        
        if ($activityStmt->execute()) {
            echo "   ✅ Test activity record added\n";
        } else {
            echo "   ❌ Error adding activity: " . $conn->error . "\n";
        }
    } else {
        echo "   ⚠️ No videos found in course $testCourseId\n";
    }

    // 5. Verify the data
    echo "\n5. Verifying data...\n";
    
    // Check activity log
    $result = $conn->query("SELECT COUNT(*) as count FROM user_activity_log");
    $count = $result->fetch_assoc()['count'];
    echo "   Activity log records: $count\n";
    
    // Check progress records
    $result = $conn->query("SELECT COUNT(*) as count FROM user_video_progress");
    $count = $result->fetch_assoc()['count'];
    echo "   Progress records: $count\n";
    
    // Check specific user data
    $userActivityStmt = $conn->prepare("SELECT COUNT(*) as count FROM user_activity_log WHERE user_id = ?");
    $userActivityStmt->bind_param("i", $testUserId);
    $userActivityStmt->execute();
    $count = $userActivityStmt->get_result()->fetch_assoc()['count'];
    echo "   User $testUserId activity records: $count\n";

    echo "\n✅ All tables created and test data added successfully!\n";
    echo "\nNow test the analytics page:\n";
    echo "https://mycloudforge.com/admin/video_analytics.php?user_id=$testUserId&course_id=$testCourseId\n";

} catch (Exception $e) {
    echo "\n❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>\n";
?>

<style>
body { font-family: monospace; background: #f5f5f5; padding: 20px; }
pre { background: white; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
h2 { color: #333; }
</style>
