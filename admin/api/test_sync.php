<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../includes/config.php';
require_once '../includes/database.php';

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // Get recent video progress updates
    $recentUpdatesQuery = "
        SELECT 
            uvp.user_id,
            uvp.video_id,
            cv.title as video_title,
            uvp.watch_duration_seconds,
            uvp.is_completed,
            uvp.updated_at,
            u.name as user_name
        FROM user_video_progress uvp
        JOIN course_videos cv ON uvp.video_id = cv.id
        JOIN users u ON uvp.user_id = u.id
        WHERE uvp.updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY uvp.updated_at DESC
        LIMIT 10
    ";
    
    $result = $conn->query($recentUpdatesQuery);
    $recentUpdates = [];
    while ($row = $result->fetch_assoc()) {
        $recentUpdates[] = $row;
    }
    
    // Get sync statistics
    $statsQuery = "
        SELECT 
            COUNT(*) as total_progress_records,
            COUNT(CASE WHEN updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as recent_updates,
            COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed_videos,
            MAX(updated_at) as last_update
        FROM user_video_progress
    ";
    
    $statsResult = $conn->query($statsQuery);
    $stats = $statsResult->fetch_assoc();
    
    // Check activity log sync
    $activityQuery = "
        SELECT COUNT(*) as activity_count
        FROM user_activity_log 
        WHERE activity_type = 'video_progress' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ";
    
    $activityResult = $conn->query($activityQuery);
    $activityStats = $activityResult->fetch_assoc();
    
    echo json_encode([
        'success' => true,
        'message' => 'Sync test completed successfully',
        'timestamp' => date('Y-m-d H:i:s'),
        'data' => [
            'sync_status' => 'active',
            'database_connection' => 'healthy',
            'recent_updates' => $recentUpdates,
            'statistics' => [
                'total_progress_records' => (int)$stats['total_progress_records'],
                'recent_updates_count' => (int)$stats['recent_updates'],
                'completed_videos' => (int)$stats['completed_videos'],
                'last_update' => $stats['last_update'],
                'recent_activity_logs' => (int)$activityStats['activity_count']
            ],
            'flutter_integration' => [
                'api_endpoint' => '/api/video_progress.php',
                'expected_fields' => [
                    'video_id' => 'required',
                    'watch_duration_seconds' => 'optional',
                    'last_position_seconds' => 'optional',
                    'completed' => 'optional (0 or 1)'
                ],
                'authentication' => 'Bearer token required'
            ]
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Sync test failed: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
