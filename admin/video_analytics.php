<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Temporarily disable auth check for debugging
/*
// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
    exit;
}

// Check if user has admin role
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to access this page.');
    Utilities::redirect('index.php');
    exit;
}
*/

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get course ID from query parameters
$courseId = isset($_GET['course_id']) ? intval($_GET['course_id']) : 0;
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

// Debug: Log the received parameters
error_log("Video Analytics - Received parameters: User ID = $userId, Course ID = $courseId");

// Get all courses for dropdown
$coursesQuery = "SELECT id, title FROM courses ORDER BY title";
$coursesStmt = $conn->prepare($coursesQuery);
$coursesStmt->execute();
$coursesResult = $coursesStmt->get_result();
$courses = [];
while ($course = $coursesResult->fetch_assoc()) {
    $courses[] = $course;
}

// Get all users for dropdown
$usersQuery = "SELECT id, name, username FROM users ORDER BY name";
$usersStmt = $conn->prepare($usersQuery);
$usersStmt->execute();
$usersResult = $usersStmt->get_result();
$users = [];
while ($user = $usersResult->fetch_assoc()) {
    $users[] = $user;
}

// Initialize all variables with default values
$analytics = [
    'total_views' => 0,
    'total_watch_duration' => 0,
    'completed_videos' => 0,
    'last_activity' => null
];
$videos = [];
$timeline = [];
$progressPercentage = 0;
$selectedCourse = null;
$selectedUser = null;
$debugInfo = [
    'table_exists' => false,
    'total_activity_records' => 0,
    'user_activity_records' => 0,
    'video_progress_records' => 0,
    'user_progress_records' => 0,
    'timeline_count' => 0
];

if ($courseId > 0 && $userId > 0) {
    try {
        // Get course details
        $courseQuery = "SELECT * FROM courses WHERE id = ?";
        $courseStmt = $conn->prepare($courseQuery);
        $courseStmt->bind_param("i", $courseId);
        $courseStmt->execute();
        $courseResult = $courseStmt->get_result();
        $selectedCourse = $courseResult->fetch_assoc();

        // Get user details
        $userQuery = "SELECT * FROM users WHERE id = ?";
        $userStmt = $conn->prepare($userQuery);
        $userStmt->bind_param("i", $userId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();
        $selectedUser = $userResult->fetch_assoc();

        // Get enrollment details - with error handling
        $enrollment = null;
        try {
            $enrollmentQuery = "SELECT e.*,
                               DATEDIFF(CURRENT_DATE, e.start_date) as days_enrolled,
                               DATEDIFF(e.end_date, CURRENT_DATE) as days_remaining,
                               DATEDIFF(CURRENT_DATE, e.start_date) DIV 7 as weeks_enrolled
                               FROM user_course_enrollments e
                               WHERE e.user_id = ? AND e.course_id = ?";
            $enrollmentStmt = $conn->prepare($enrollmentQuery);
            $enrollmentStmt->bind_param("ii", $userId, $courseId);
            $enrollmentStmt->execute();
            $enrollmentResult = $enrollmentStmt->get_result();
            $enrollment = $enrollmentResult->fetch_assoc();
        } catch (Exception $e) {
            error_log("Enrollment query error: " . $e->getMessage());
            // Create dummy enrollment data
            $enrollment = [
                'status' => 'active',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+30 days')),
                'days_enrolled' => 1,
                'days_remaining' => 30,
                'weeks_enrolled' => 1
            ];
        }

        // If no enrollment found, create dummy data
        if (!$enrollment) {
            $enrollment = [
                'status' => 'active',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+30 days')),
                'days_enrolled' => 1,
                'days_remaining' => 30,
                'weeks_enrolled' => 1
            ];
        }

    // Get videos with progress - Handle both old and new column names
    $videosQuery = "SELECT v.*,
                   IFNULL(p.is_unlocked, 1) as is_unlocked,
                   IFNULL(COALESCE(p.is_completed, p.completed), 0) as is_completed,
                   p.unlock_date, p.completion_date,
                   COALESCE(p.watch_duration_seconds, p.watch_time_seconds, 0) as watch_duration_seconds,
                   COALESCE(p.last_position_seconds, 0) as last_position_seconds,
                   (SELECT COUNT(*) FROM user_activity_log
                    WHERE user_id = ? AND activity_type = 'video_progress'
                    AND related_id = v.id) as view_count
                   FROM course_videos v
                   LEFT JOIN user_video_progress p ON v.id = p.video_id AND p.user_id = ?
                   WHERE v.course_id = ?
                   ORDER BY v.week_number, v.sequence_number";
    $videosStmt = $conn->prepare($videosQuery);
    $videosStmt->bind_param("iii", $userId, $userId, $courseId);
    $videosStmt->execute();
    $videosResult = $videosStmt->get_result();

    while ($video = $videosResult->fetch_assoc()) {
        $videos[] = $video;
    }

    // Debug: Check if user_activity_log table exists and has data
    $debugQuery = "SHOW TABLES LIKE 'user_activity_log'";
    $debugResult = $conn->query($debugQuery);
    $debugInfo['table_exists'] = $debugResult && $debugResult->num_rows > 0;

    // If table doesn't exist, create it
    if (!$debugInfo['table_exists']) {
        try {
            $createTableSql = "
            CREATE TABLE user_activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                activity_type VARCHAR(50) NOT NULL,
                related_id INT NULL,
                details JSON NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_activity_type (activity_type),
                INDEX idx_user_type_related (user_id, activity_type, related_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            if ($conn->query($createTableSql)) {
                $debugInfo['table_exists'] = true;
                $debugInfo['table_created'] = true;
                error_log("Created user_activity_log table successfully");
            } else {
                $debugInfo['table_creation_error'] = $conn->error;
                error_log("Failed to create user_activity_log table: " . $conn->error);
            }
        } catch (Exception $e) {
            $debugInfo['table_creation_error'] = $e->getMessage();
            error_log("Exception creating user_activity_log table: " . $e->getMessage());
        }
    }

    if ($debugInfo['table_exists']) {
        // Check total records in activity log
        try {
            $countQuery = "SELECT COUNT(*) as total_records FROM user_activity_log";
            $countResult = $conn->query($countQuery);
            $debugInfo['total_activity_records'] = $countResult ? $countResult->fetch_assoc()['total_records'] : 0;

            // Check records for this user
            $userCountQuery = "SELECT COUNT(*) as user_records FROM user_activity_log WHERE user_id = ?";
            $userCountStmt = $conn->prepare($userCountQuery);
            $userCountStmt->bind_param("i", $userId);
            $userCountStmt->execute();
            $userResult = $userCountStmt->get_result();
            $debugInfo['user_activity_records'] = $userResult ? $userResult->fetch_assoc()['user_records'] : 0;

            // Check video_progress records for this user
            $videoProgressQuery = "SELECT COUNT(*) as video_progress_records FROM user_activity_log WHERE user_id = ? AND activity_type = 'video_progress'";
            $videoProgressStmt = $conn->prepare($videoProgressQuery);
            $videoProgressStmt->bind_param("i", $userId);
            $videoProgressStmt->execute();
            $videoProgressResult = $videoProgressStmt->get_result();
            $debugInfo['video_progress_records'] = $videoProgressResult ? $videoProgressResult->fetch_assoc()['video_progress_records'] : 0;
        } catch (Exception $e) {
            error_log("Debug info collection error: " . $e->getMessage());
            $debugInfo['total_activity_records'] = 0;
            $debugInfo['user_activity_records'] = 0;
            $debugInfo['video_progress_records'] = 0;
        }

        // If no activity records exist, create some test data
        if ($debugInfo['total_activity_records'] == 0 && $debugInfo['user_activity_records'] == 0) {
            try {
                // Get first video from the course
                $testVideoQuery = "SELECT id, title FROM course_videos WHERE course_id = ? LIMIT 1";
                $testVideoStmt = $conn->prepare($testVideoQuery);
                $testVideoStmt->bind_param("i", $courseId);
                $testVideoStmt->execute();
                $testVideo = $testVideoStmt->get_result()->fetch_assoc();

                if ($testVideo) {
                    // Add test progress record
                    $testProgressSql = "
                    INSERT INTO user_video_progress
                    (user_id, video_id, watch_duration_seconds, last_position_seconds, is_completed, is_unlocked)
                    VALUES (?, ?, 180, 180, 1, 1)
                    ON DUPLICATE KEY UPDATE
                    watch_duration_seconds = 180,
                    last_position_seconds = 180,
                    is_completed = 1,
                    updated_at = NOW()
                    ";

                    $testProgressStmt = $conn->prepare($testProgressSql);
                    $testProgressStmt->bind_param("ii", $userId, $testVideo['id']);
                    $testProgressStmt->execute();

                    // Add test activity log
                    $testActivitySql = "
                    INSERT INTO user_activity_log (user_id, activity_type, related_id, details)
                    VALUES (?, 'video_progress', ?, ?)
                    ";

                    $testDetails = json_encode([
                        'action' => 'complete',
                        'watch_duration' => 180,
                        'last_position' => 180,
                        'is_completed' => true,
                        'timestamp' => date('Y-m-d H:i:s')
                    ]);

                    $testActivityStmt = $conn->prepare($testActivitySql);
                    $testActivityStmt->bind_param("iis", $userId, $testVideo['id'], $testDetails);
                    $testActivityStmt->execute();

                    $debugInfo['test_data_created'] = true;
                    error_log("Created test data for user $userId, video {$testVideo['id']}");
                }
            } catch (Exception $e) {
                $debugInfo['test_data_error'] = $e->getMessage();
                error_log("Error creating test data: " . $e->getMessage());
            }
        }
    }

    // Get overall analytics - Ensure all values are properly initialized
    $analytics = [
        'total_views' => 0,
        'total_watch_duration' => 0,
        'completed_videos' => 0,
        'last_activity' => null
    ];

    // Try to get analytics from activity log first
    try {
        $analyticsQuery = "SELECT
                          COUNT(DISTINCT al.id) as total_views,
                          SUM(CASE
                              WHEN al.details LIKE '%watch_duration%' THEN
                                  CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(al.details, '\"watch_duration\":', -1), ',', 1) AS UNSIGNED)
                              ELSE 0
                          END) as total_watch_duration,
                          COUNT(DISTINCT CASE
                              WHEN al.details LIKE '%\"is_completed\":true%' OR al.details LIKE '%\"is_completed\":1%'
                              THEN al.related_id
                          END) as completed_videos,
                          MAX(al.created_at) as last_activity
                          FROM user_activity_log al
                          JOIN course_videos cv ON al.related_id = cv.id
                          WHERE al.user_id = ? AND cv.course_id = ? AND al.activity_type = 'video_progress'
                          GROUP BY al.user_id, cv.course_id";
        $analyticsStmt = $conn->prepare($analyticsQuery);
        $analyticsStmt->bind_param("ii", $userId, $courseId);
        $analyticsStmt->execute();
        $analyticsResult = $analyticsStmt->get_result();
        $activityAnalytics = $analyticsResult->fetch_assoc();

        if ($activityAnalytics) {
            // Merge with defaults to ensure all keys exist
            $analytics = array_merge($analytics, array_filter($activityAnalytics, function($value) {
                return $value !== null;
            }));
        }
    } catch (Exception $e) {
        error_log("Activity analytics error: " . $e->getMessage());
    }

    // Fallback to progress table if no activity data
    if ($analytics['total_views'] == 0) {
        try {
            $progressQuery = "SELECT
                             COUNT(DISTINCT p.video_id) as total_views,
                             SUM(p.watch_duration_seconds) as total_watch_duration,
                             COUNT(DISTINCT CASE WHEN p.is_completed = 1 THEN p.video_id END) as completed_videos,
                             MAX(p.updated_at) as last_activity
                             FROM user_video_progress p
                             JOIN course_videos cv ON p.video_id = cv.id
                             WHERE p.user_id = ? AND cv.course_id = ? AND p.watch_duration_seconds > 0";
            $progressStmt = $conn->prepare($progressQuery);
            $progressStmt->bind_param("ii", $userId, $courseId);
            $progressStmt->execute();
            $progressResult = $progressStmt->get_result();
            $progressAnalytics = $progressResult->fetch_assoc();

            if ($progressAnalytics && $progressAnalytics['total_views'] > 0) {
                // Merge with defaults to ensure all keys exist
                $analytics = array_merge($analytics, array_filter($progressAnalytics, function($value) {
                    return $value !== null;
                }));
            }
        } catch (Exception $e) {
            error_log("Progress analytics error: " . $e->getMessage());
        }
    }

    // Calculate progress percentage
    $completedCount = $analytics['completed_videos'] ?? 0;
    $totalVideos = count($videos);
    $progressPercentage = $totalVideos > 0 ? round(($completedCount / $totalVideos) * 100) : 0;

    // Get activity timeline - Simplified to avoid errors
    $timeline = [];
    try {
        $timelineQuery = "SELECT
                         al.id,
                         al.created_at,
                         al.details,
                         al.related_id,
                         cv.title as video_title,
                         cv.week_number
                         FROM user_activity_log al
                         JOIN course_videos cv ON al.related_id = cv.id
                         WHERE al.user_id = ? AND cv.course_id = ? AND al.activity_type = 'video_progress'
                         ORDER BY al.created_at DESC
                         LIMIT 50";
        $timelineStmt = $conn->prepare($timelineQuery);
        $timelineStmt->bind_param("ii", $userId, $courseId);
        $timelineStmt->execute();
        $timelineResult = $timelineStmt->get_result();

        while ($activity = $timelineResult->fetch_assoc()) {
            // Parse JSON details safely
            $details = json_decode($activity['details'], true);
            if ($details) {
                $activity['watch_duration'] = $details['watch_duration'] ?? 0;
                $activity['last_position'] = $details['last_position'] ?? 0;
                $activity['is_completed'] = $details['is_completed'] ?? false;
                $activity['action'] = $details['action'] ?? 'unknown';
            } else {
                $activity['watch_duration'] = 0;
                $activity['last_position'] = 0;
                $activity['is_completed'] = false;
                $activity['action'] = 'unknown';
            }
            $timeline[] = $activity;
        }
    } catch (Exception $e) {
        error_log("Timeline query error: " . $e->getMessage());
        $timeline = [];
    }

    // Debug: Store timeline count
    $debugInfo['timeline_count'] = count($timeline);

    // Debug: Get sample activity records without JOIN to see if data exists
    $sampleQuery = "SELECT al.*, al.details FROM user_activity_log al WHERE al.user_id = ? AND al.activity_type = 'video_progress' ORDER BY al.created_at DESC LIMIT 5";
    $sampleStmt = $conn->prepare($sampleQuery);
    $sampleStmt->bind_param("i", $userId);
    $sampleStmt->execute();
    $sampleResult = $sampleStmt->get_result();
    $debugInfo['sample_activities'] = [];
    while ($sample = $sampleResult->fetch_assoc()) {
        $debugInfo['sample_activities'][] = $sample;
    }

    // Debug: Check user_video_progress table structure
    $progressTableQuery = "SHOW COLUMNS FROM user_video_progress";
    $progressTableResult = $conn->query($progressTableQuery);
    $debugInfo['progress_table_columns'] = [];
    while ($column = $progressTableResult->fetch_assoc()) {
        $debugInfo['progress_table_columns'][] = $column['Field'];
    }

    // Debug: Check if there are any progress records for this user
    $progressCountQuery = "SELECT COUNT(*) as progress_records FROM user_video_progress WHERE user_id = ?";
    $progressCountStmt = $conn->prepare($progressCountQuery);
    $progressCountStmt->bind_param("i", $userId);
    $progressCountStmt->execute();
    $debugInfo['user_progress_records'] = $progressCountStmt->get_result()->fetch_assoc()['progress_records'];

    // Debug: Get sample progress records
    $sampleProgressQuery = "SELECT * FROM user_video_progress WHERE user_id = ? LIMIT 3";
    $sampleProgressStmt = $conn->prepare($sampleProgressQuery);
    $sampleProgressStmt->bind_param("i", $userId);
    $sampleProgressStmt->execute();
    $sampleProgressResult = $sampleProgressStmt->get_result();
    $debugInfo['sample_progress'] = [];
    while ($progress = $sampleProgressResult->fetch_assoc()) {
        $debugInfo['sample_progress'][] = $progress;
    }

    } catch (Exception $e) {
        error_log("Video analytics error: " . $e->getMessage());
        // Don't reset user and course data, just set defaults for other variables
        if (!isset($enrollment)) {
            $enrollment = [
                'status' => 'active',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+30 days')),
                'days_enrolled' => 1,
                'days_remaining' => 30,
                'weeks_enrolled' => 1
            ];
        }
        if (!isset($videos)) $videos = [];
        if (!isset($analytics)) $analytics = ['total_views' => 0, 'total_watch_duration' => 0, 'completed_videos' => 0, 'last_activity' => null];
        if (!isset($timeline)) $timeline = [];
        $debugInfo['error'] = $e->getMessage();
    }
}

// Include header
$pageTitle = 'Video Analytics Dashboard';
require_once 'includes/header.php';
?>

<!-- Minimalist CSS for clean analytics interface -->
<style>
:root {
    --primary-color: #2563eb;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-900: #111827;
}

body {
    background-color: var(--gray-50);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--gray-900);
    line-height: 1.6;
}

.analytics-header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.analytics-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.stat-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-card.primary { border-left: 4px solid var(--primary-color); }
.stat-card.success { border-left: 4px solid var(--success-color); }
.stat-card.warning { border-left: 4px solid var(--warning-color); }
.stat-card.danger { border-left: 4px solid var(--danger-color); }

.user-selector {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.timeline-item {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    border-left: 3px solid var(--primary-color);
}

.progress-bar {
    height: 6px;
    background-color: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar .progress {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.btn-simple {
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    border: 1px solid var(--gray-300);
    background: white;
    color: var(--gray-700);
    transition: all 0.2s ease;
}

.btn-simple:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
    border-color: #1d4ed8;
}

.form-control {
    border-radius: 6px;
    border: 1px solid var(--gray-300);
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
    outline: none;
}

.video-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.video-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.text-muted {
    color: var(--gray-600);
}

.text-small {
    font-size: 0.875rem;
}

.border-bottom {
    border-bottom: 1px solid var(--gray-200);
}

.auto-refresh {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    z-index: 1000;
}

.sync-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.sync-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
}

.sync-indicator.syncing {
    background: var(--warning-color);
    animation: pulse 1s infinite;
}

.sync-indicator.error {
    background: var(--danger-color);
}
</style>
</style>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="analytics-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2 fw-bold text-gray-900">Video Analytics</h1>
                    <?php if ($selectedUser && $selectedCourse): ?>
                        <p class="text-muted mb-0">
                            <span class="fw-medium"><?php echo htmlspecialchars($selectedUser['name']); ?></span>
                            <span class="mx-2">•</span>
                            <span><?php echo htmlspecialchars($selectedCourse['title']); ?></span>
                        </p>
                    <?php else: ?>
                        <p class="text-muted mb-0">Select a user and course to view analytics</p>
                    <?php endif; ?>
                </div>
                <div class="col-md-4 text-end">
                    <div class="sync-status">
                        <div class="sync-indicator" id="syncIndicator"></div>
                        <span id="syncStatus">Data synced</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User and Course Selection -->
    <div class="user-selector">
        <div class="row align-items-end">
            <div class="col-md-10">
                <form method="get" action="video_analytics.php" class="row g-3">
                    <div class="col-md-5">
                        <label for="user_id" class="form-label text-small fw-medium">User</label>
                        <select class="form-control" id="user_id" name="user_id" required>
                            <option value="">Select user...</option>
                            <?php foreach ($users as $user): ?>
                                <option value="<?php echo $user['id']; ?>" <?php echo $userId == $user['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-5">
                        <label for="course_id" class="form-label text-small fw-medium">Course</label>
                        <select class="form-control" id="course_id" name="course_id" required>
                            <option value="">Select course...</option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>" <?php echo $courseId == $course['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            View Analytics
                        </button>
                    </div>
                </form>
            </div>

            <div class="col-md-2 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <button type="button" class="btn btn-simple" onclick="refreshData()" title="Refresh">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <?php if ($selectedUser): ?>
                        <a href="users.php" class="btn btn-simple" title="Back">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Debug Information (temporary) -->
    <?php if (isset($_GET['debug'])): ?>
    <div class="alert alert-info">
        <h5>Debug Information:</h5>
        <ul>
            <li>User ID from URL: <?php echo $userId; ?></li>
            <li>Course ID from URL: <?php echo $courseId; ?></li>
            <li>Selected User: <?php echo $selectedUser ? $selectedUser['name'] : 'NULL'; ?></li>
            <li>Selected Course: <?php echo $selectedCourse ? $selectedCourse['title'] : 'NULL'; ?></li>
            <li>Condition ($selectedCourse && $selectedUser): <?php echo ($selectedCourse && $selectedUser) ? 'TRUE' : 'FALSE'; ?></li>
            <li>Table exists: <?php echo $debugInfo['table_exists'] ? 'YES' : 'NO'; ?></li>
            <li>Activity records: <?php echo $debugInfo['total_activity_records'] ?? 0; ?></li>
        </ul>
    </div>
    <?php endif; ?>

    <?php if ($selectedCourse && $selectedUser): ?>
        <!-- Analytics Overview -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stat-card primary">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="text-small text-muted mb-1">Total Views</div>
                            <div class="h4 mb-0 fw-bold"><?php echo number_format($analytics['total_views'] ?? 0); ?></div>
                        </div>
                        <i class="fas fa-eye text-muted"></i>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="stat-card success">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="text-small text-muted mb-1">Completed</div>
                            <div class="h4 mb-0 fw-bold">
                                <?php
                                $completedCount = $analytics['completed_videos'] ?? 0;
                                $totalVideos = count($videos);
                                echo "$completedCount / $totalVideos";
                                ?>
                            </div>
                            <div class="text-small text-muted">
                                <?php echo $totalVideos > 0 ? round(($completedCount / $totalVideos) * 100) : 0; ?>% completion rate
                            </div>
                        </div>
                        <i class="fas fa-check-circle text-muted"></i>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="stat-card warning">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="text-small text-muted mb-1">Watch Time</div>
                            <div class="h4 mb-0 fw-bold">
                                <?php
                                $totalSeconds = $analytics['total_watch_duration'] ?? 0;
                                $hours = floor($totalSeconds / 3600);
                                $minutes = floor(($totalSeconds % 3600) / 60);
                                echo $hours > 0 ? "{$hours}h {$minutes}m" : "{$minutes}m";
                                ?>
                            </div>
                            <div class="text-small text-muted">
                                Avg: <?php echo $totalVideos > 0 ? round($totalSeconds / $totalVideos / 60) : 0; ?>m per video
                            </div>
                        </div>
                        <i class="fas fa-clock text-muted"></i>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="stat-card danger">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="text-small text-muted mb-1">Last Activity</div>
                            <div class="h4 mb-0 fw-bold">
                                <?php
                                if ($analytics['last_activity']) {
                                    $lastActivity = strtotime($analytics['last_activity']);
                                    $now = time();
                                    $diff = $now - $lastActivity;

                                    if ($diff < 3600) {
                                        echo floor($diff / 60) . 'm ago';
                                    } elseif ($diff < 86400) {
                                        echo floor($diff / 3600) . 'h ago';
                                    } else {
                                        echo date('M d', $lastActivity);
                                    }
                                } else {
                                    echo 'No activity';
                                }
                                ?>
                            </div>
                            <div class="text-small text-muted">
                                <?php echo count($timeline); ?> activities logged
                            </div>
                        </div>
                        <i class="fas fa-calendar-check text-muted"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Course Overview -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="analytics-card h-100">
                    <div class="card-header bg-transparent border-0 py-3 d-flex justify-content-between align-items-center">
                        <h5 class="m-0 fw-bold text-dark">
                            <i class="fas fa-graduation-cap me-2 text-primary"></i>Course Details
                        </h5>
                        <span class="badge rounded-pill px-3 py-2 bg-<?php echo $enrollment['status'] === 'active' ? 'success' : ($enrollment['status'] === 'completed' ? 'primary' : 'secondary'); ?>">
                            <i class="fas fa-circle me-1" style="font-size: 0.5rem;"></i>
                            <?php echo ucfirst($enrollment['status']); ?>
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="course-info">
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-play-circle text-primary me-2"></i>
                                    <strong><?php echo htmlspecialchars($selectedCourse['title']); ?></strong>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-tag text-info me-2"></i>
                                    <span><?php echo htmlspecialchars($selectedCourse['category'] ?? 'General'); ?></span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar-alt text-warning me-2"></i>
                                    <span><?php echo $selectedCourse['duration_weeks']; ?> weeks duration</span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-rupee-sign text-success me-2"></i>
                                    <span>₹<?php echo number_format($selectedCourse['price'], 2); ?></span>
                                </div>
                            </div>
                            <div class="info-item mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar-check text-primary me-2"></i>
                                    <span>Started: <?php echo date('M d, Y', strtotime($enrollment['start_date'])); ?></span>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-flag-checkered text-danger me-2"></i>
                                    <span>Ends: <?php echo date('M d, Y', strtotime($enrollment['end_date'])); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="analytics-card h-100">
                    <div class="card-header bg-transparent border-0 py-3">
                        <h5 class="m-0 fw-bold text-dark">
                            <i class="fas fa-chart-pie me-2 text-success"></i>Progress Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Progress Circle -->
                        <div class="text-center mb-4">
                            <div class="position-relative d-inline-block">
                                <svg width="120" height="120" class="progress-ring">
                                    <circle cx="60" cy="60" r="50" stroke="#e9ecef" stroke-width="8" fill="transparent"/>
                                    <circle cx="60" cy="60" r="50" stroke="url(#gradient)" stroke-width="8" fill="transparent"
                                            stroke-dasharray="314" stroke-dashoffset="<?php echo 314 - (314 * $progressPercentage / 100); ?>"
                                            stroke-linecap="round" transform="rotate(-90 60 60)"/>
                                    <defs>
                                        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#667eea"/>
                                            <stop offset="100%" style="stop-color:#764ba2"/>
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="position-absolute top-50 start-50 translate-middle text-center">
                                    <h3 class="mb-0 fw-bold text-primary"><?php echo $progressPercentage; ?>%</h3>
                                    <small class="text-muted">Complete</small>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Stats -->
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="fw-bold text-primary"><?php echo $enrollment['days_enrolled']; ?></h6>
                                    <small class="text-muted">Days Enrolled</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="fw-bold text-success"><?php echo $enrollment['weeks_enrolled']; ?>/<?php echo $selectedCourse['duration_weeks']; ?></h6>
                                    <small class="text-muted">Weeks Done</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h6 class="fw-bold text-warning"><?php echo max(0, $enrollment['days_remaining']); ?></h6>
                                <small class="text-muted">Days Left</small>
                            </div>
                        </div>

                        <!-- Timeline Progress -->
                        <div class="mt-4">
                            <div class="d-flex justify-content-between mb-2">
                                <small class="text-muted">Course Timeline</small>
                                <small class="text-muted">Week <?php echo min($enrollment['weeks_enrolled'] + 1, $selectedCourse['duration_weeks']); ?> of <?php echo $selectedCourse['duration_weeks']; ?></small>
                            </div>
                            <div class="progress-bar-modern">
                                <div class="progress" style="width: <?php echo min(100, ($enrollment['weeks_enrolled'] / $selectedCourse['duration_weeks']) * 100); ?>%"></div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2 mt-4">
                            <a href="user_edit.php?id=<?php echo $userId; ?>" class="btn btn-outline-primary btn-modern flex-fill">
                                <i class="fas fa-edit me-1"></i>Manage
                            </a>
                            <button type="button" class="btn btn-outline-success btn-modern flex-fill" data-bs-toggle="modal" data-bs-target="#unlockVideosModal">
                                <i class="fas fa-unlock me-1"></i>Unlock
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Progress -->
        <div class="analytics-card">
            <div class="card-header bg-white border-bottom p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-medium">Video Progress</h5>
                    <div class="text-small text-muted">
                        <?php echo $progressPercentage; ?>% complete • <?php echo count($videos); ?> videos
                    </div>
                </div>
            </div>

            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-sm mb-0">
                        <thead class="bg-light">
                            <tr>
                                <th class="border-0 text-small fw-medium text-muted py-3 px-3">Video</th>
                                <th class="border-0 text-small fw-medium text-muted py-3">Progress</th>
                                <th class="border-0 text-small fw-medium text-muted py-3">Watch Time</th>
                                <th class="border-0 text-small fw-medium text-muted py-3">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($videos as $video): ?>
                                <tr>
                                    <td class="border-0 py-3 px-3">
                                        <div>
                                            <div class="fw-medium"><?php echo htmlspecialchars($video['title']); ?></div>
                                            <div class="text-small text-muted">Week <?php echo $video['week_number']; ?></div>
                                        </div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <div class="progress-bar mb-1">
                                            <?php
                                            $progressPercent = 0;
                                            if ($video['duration_minutes'] && $video['watch_duration_seconds']) {
                                                $totalVideoSeconds = $video['duration_minutes'] * 60;
                                                $progressPercent = min(100, round(($video['watch_duration_seconds'] / $totalVideoSeconds) * 100));
                                            }
                                            ?>
                                            <div class="progress" style="width: <?php echo $progressPercent; ?>%"></div>
                                        </div>
                                        <div class="text-small text-muted"><?php echo $progressPercent; ?>%</div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <div class="text-small">
                                            <?php
                                            $seconds = $video['watch_duration_seconds'] ?? 0;
                                            $minutes = floor($seconds / 60);
                                            $remainingSeconds = $seconds % 60;
                                            echo $minutes . "m " . $remainingSeconds . "s";
                                            ?>
                                        </div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <?php if ($video['is_completed']): ?>
                                            <span class="badge bg-success text-white">Completed</span>
                                        <?php elseif ($video['watch_duration_seconds'] > 0): ?>
                                            <span class="badge bg-warning text-white">In Progress</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary text-white">Not Started</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

        <!-- Activity Timeline -->
        <div class="analytics-card">
            <div class="card-header bg-white border-bottom p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 fw-medium">Recent Activity</h5>
                    <div class="text-small text-muted"><?php echo count($timeline); ?> activities</div>
                </div>
            </div>

            <div class="card-body p-0">
                <?php if (empty($timeline)): ?>
                    <div class="text-center py-4">
                        <div class="text-muted">No activity recorded yet</div>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($timeline as $activity): ?>
                            <div class="timeline-item list-group-item border-0 py-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="fw-medium mb-1">
                                            <?php echo htmlspecialchars($activity['video_title']); ?>
                                        </div>
                                        <div class="text-small text-muted mb-2">
                                            Week <?php echo $activity['week_number']; ?> •
                                            <?php echo ucfirst($activity['action']); ?> •
                                            <?php echo floor($activity['watch_duration'] / 60); ?>m <?php echo $activity['watch_duration'] % 60; ?>s
                                        </div>
                                        <?php if ($activity['is_completed']): ?>
                                            <span class="badge bg-success text-white">Completed</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end text-small text-muted">
                                        <div><?php echo date('M d', strtotime($activity['created_at'])); ?></div>
                                        <div><?php echo date('H:i', strtotime($activity['created_at'])); ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        </div>

        <!-- Debug Information Section (only show if there are actual issues or debug mode) -->
        <?php
        $hasIssues = !empty($debugInfo) && (
            !($debugInfo['table_exists'] ?? true) ||
            ($debugInfo['total_activity_records'] ?? 0) == 0 ||
            ($debugInfo['user_activity_records'] ?? 0) == 0
        );
        $showDebug = isset($_GET['debug']) || $hasIssues;
        ?>
        <?php if ($showDebug): ?>
        <div class="analytics-card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-bug me-2"></i>Debug Information
                    <small class="text-muted">(Troubleshooting video analytics)</small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">Database Status</h6>
                        <ul class="list-unstyled">
                            <li><strong>Activity Log Table:</strong> <?php echo $debugInfo['table_exists'] ? '✅ Exists' : '❌ Missing'; ?></li>
                            <li><strong>Total Activity Records:</strong> <?php echo $debugInfo['total_activity_records'] ?? 0; ?></li>
                            <li><strong>User Activity Records:</strong> <?php echo $debugInfo['user_activity_records'] ?? 0; ?></li>
                            <li><strong>Video Progress Records:</strong> <?php echo $debugInfo['video_progress_records'] ?? 0; ?></li>
                            <li><strong>User Progress Records:</strong> <?php echo $debugInfo['user_progress_records'] ?? 0; ?></li>
                            <li><strong>Timeline Count:</strong> <?php echo $debugInfo['timeline_count'] ?? 0; ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold">Progress Table Columns</h6>
                        <div class="small">
                            <?php if (!empty($debugInfo['progress_table_columns'])): ?>
                                <code><?php echo implode(', ', $debugInfo['progress_table_columns']); ?></code>
                            <?php else: ?>
                                <span class="text-muted">No columns found</span>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($debugInfo['sample_activities'])): ?>
                        <h6 class="fw-bold mt-3">Sample Activity Records</h6>
                        <div class="small">
                            <?php foreach ($debugInfo['sample_activities'] as $activity): ?>
                                <div class="border p-2 mb-1 rounded">
                                    <strong>ID:</strong> <?php echo $activity['id']; ?><br>
                                    <strong>Type:</strong> <?php echo $activity['activity_type']; ?><br>
                                    <strong>Details:</strong> <code><?php echo htmlspecialchars(substr($activity['details'], 0, 100)); ?>...</code>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($debugInfo['sample_progress'])): ?>
                        <h6 class="fw-bold mt-3">Sample Progress Records</h6>
                        <div class="small">
                            <?php foreach ($debugInfo['sample_progress'] as $progress): ?>
                                <div class="border p-2 mb-1 rounded">
                                    <strong>Video ID:</strong> <?php echo $progress['video_id']; ?><br>
                                    <?php foreach ($progress as $key => $value): ?>
                                        <?php if ($key !== 'video_id' && $key !== 'user_id'): ?>
                                            <strong><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:</strong> <?php echo $value; ?><br>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($hasIssues): ?>
                <div class="alert alert-warning mt-3">
                    <h6 class="alert-heading">⚠️ Issues Detected:</h6>
                    <ul class="mb-3">
                        <?php if (!($debugInfo['table_exists'] ?? true)): ?>
                            <li>Activity log table is missing</li>
                        <?php endif; ?>
                        <?php if (($debugInfo['total_activity_records'] ?? 0) == 0): ?>
                            <li>No activity records found in the database</li>
                        <?php endif; ?>
                        <?php if (($debugInfo['user_activity_records'] ?? 0) == 0): ?>
                            <li>No activity records found for this user</li>
                        <?php endif; ?>
                        <?php if (($debugInfo['user_progress_records'] ?? 0) == 0): ?>
                            <li>No progress records found for this user</li>
                        <?php endif; ?>
                    </ul>
                    <div class="d-flex gap-2">
                        <a href="fix_analytics_database.php" class="btn btn-sm btn-warning">
                            <i class="fas fa-wrench me-1"></i>Fix Database Issues
                        </a>
                        <a href="create_sample_data.php" class="btn btn-sm btn-info">
                            <i class="fas fa-plus me-1"></i>Add Sample Data
                        </a>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-success mt-3">
                    <h6 class="alert-heading">✅ System Status: Good</h6>
                    <p class="mb-0">All database tables exist and contain data. Analytics should be working properly.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

    <?php elseif ($userId > 0 || $courseId > 0): ?>
        <div class="analytics-card text-center py-5">
            <div class="mb-3">
                <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
            </div>
            <h5 class="text-warning">Incomplete Selection</h5>
            <p class="text-muted">Please select both a user and a course to view detailed analytics.</p>
        </div>
    <?php else: ?>
        <div class="analytics-card text-center py-5">
            <div class="mb-4">
                <i class="fas fa-chart-line fa-4x text-muted opacity-50"></i>
            </div>
            <h4 class="text-muted mb-3">Welcome to Video Analytics</h4>
            <p class="text-muted mb-4">Get detailed insights into user video engagement and learning progress.</p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                                <h6>View Tracking</h6>
                                <small class="text-muted">Monitor video views and engagement</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-chart-pie fa-2x text-success mb-2"></i>
                                <h6>Progress Analytics</h6>
                                <small class="text-muted">Track completion rates and progress</small>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="feature-item">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h6>Time Analysis</h6>
                                <small class="text-muted">Analyze watch time and patterns</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Unlock Videos Modal -->
<?php if ($selectedCourse && $selectedUser): ?>
<div class="modal fade" id="unlockVideosModal" tabindex="-1" aria-labelledby="unlockVideosModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unlockVideosModalLabel">Unlock Videos for <?php echo htmlspecialchars($selectedUser['name']); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="mb-4">Select which videos to unlock for this user. This will override the automatic weekly unlocking schedule.</p>

                <form action="bulk_unlock_videos.php" method="post" id="unlockVideosForm">
                    <input type="hidden" name="user_id" value="<?php echo $userId; ?>">
                    <input type="hidden" name="course_id" value="<?php echo $courseId; ?>">

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllVideos">
                                        </div>
                                    </th>
                                    <th width="10%">Week</th>
                                    <th width="55%">Video Title</th>
                                    <th width="15%">Duration</th>
                                    <th width="15%">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($videos as $video): ?>
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input video-checkbox" type="checkbox"
                                                       name="video_ids[]" value="<?php echo $video['id']; ?>"
                                                       <?php echo $video['is_unlocked'] ? 'checked disabled' : ''; ?>>
                                            </div>
                                        </td>
                                        <td>Week <?php echo $video['week_number']; ?></td>
                                        <td><?php echo htmlspecialchars($video['title']); ?></td>
                                        <td><?php echo $video['duration_minutes'] ? $video['duration_minutes'].' min' : 'N/A'; ?></td>
                                        <td>
                                            <?php if (!$video['is_unlocked']): ?>
                                                <span class="badge bg-secondary">Locked</span>
                                            <?php elseif ($video['is_completed']): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="unlockVideosForm" class="btn btn-success">
                    <i class="fas fa-unlock me-1"></i> Unlock Selected Videos
                </button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
/* Additional modern styles */
.stat-mini {
    text-align: center;
}

.feature-item {
    text-align: center;
    padding: 1rem;
}

.info-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.progress-ring {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

.activity-timeline {
    max-height: 600px;
    overflow-y: auto;
}

.activity-timeline::-webkit-scrollbar {
    width: 6px;
}

.activity-timeline::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.activity-timeline::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 10px;
}

.activity-timeline::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

@media (max-width: 768px) {
    .analytics-header {
        padding: 1rem 0;
        margin-bottom: 1rem;
    }

    .analytics-header h1 {
        font-size: 2rem;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .timeline-item-modern {
        flex-direction: column;
        text-align: center;
    }

    .activity-icon {
        margin-bottom: 1rem;
    }
}
</style>



<?php require_once 'includes/footer.php'; ?>

<!-- Minimalist JavaScript for Core Functionality -->
<script>
// Refresh data function
function refreshData() {
    const currentUrl = new URL(window.location);
    const userId = currentUrl.searchParams.get('user_id');
    const courseId = currentUrl.searchParams.get('course_id');

    if (userId && courseId) {
        updateSyncStatus('syncing', 'Refreshing...');

        // In a real implementation, this would make an AJAX call to check for updates
        setTimeout(() => {
            window.location.reload();
        }, 500);
    } else {
        alert('Please select a user and course first.');
    }
}

// Update sync status indicator
function updateSyncStatus(status, message) {
    const indicator = document.getElementById('syncIndicator');
    const statusText = document.getElementById('syncStatus');

    if (indicator && statusText) {
        indicator.className = `sync-indicator ${status}`;
        statusText.textContent = message;
    }
}

// Check for data updates periodically
function checkForUpdates() {
    const currentUrl = new URL(window.location);
    const userId = currentUrl.searchParams.get('user_id');
    const courseId = currentUrl.searchParams.get('course_id');

    if (userId && courseId) {
        // In a real implementation, this would check the API for new data
        // For now, just update the sync status
        updateSyncStatus('', 'Data synced');
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Check for updates every 30 seconds
    setInterval(checkForUpdates, 30000);

    // Initial sync status
    updateSyncStatus('', 'Data synced');

    // Add auto-refresh button
    const autoRefreshBtn = document.createElement('button');
    autoRefreshBtn.className = 'auto-refresh';
    autoRefreshBtn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Auto Refresh: ON';
    autoRefreshBtn.onclick = toggleAutoRefresh;
    document.body.appendChild(autoRefreshBtn);
});

// Toggle auto-refresh functionality
let autoRefreshEnabled = true;
let autoRefreshInterval;

function toggleAutoRefresh() {
    const btn = document.querySelector('.auto-refresh');

    if (autoRefreshEnabled) {
        autoRefreshEnabled = false;
        btn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Auto Refresh: OFF';
        btn.style.background = 'var(--gray-600)';
        clearInterval(autoRefreshInterval);
    } else {
        autoRefreshEnabled = true;
        btn.innerHTML = '<i class="fas fa-sync-alt me-1"></i>Auto Refresh: ON';
        btn.style.background = 'var(--primary-color)';

        // Refresh every 2 minutes when enabled
        autoRefreshInterval = setInterval(() => {
            const currentUrl = new URL(window.location);
            const userId = currentUrl.searchParams.get('user_id');
            const courseId = currentUrl.searchParams.get('course_id');

            if (userId && courseId) {
                updateSyncStatus('syncing', 'Auto refreshing...');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        }, 120000); // 2 minutes
    }
}

// Test Flutter app connectivity
function testFlutterSync() {
    updateSyncStatus('syncing', 'Testing connection...');

    // Simulate API test
    setTimeout(() => {
        updateSyncStatus('', 'Connection verified');
        setTimeout(() => {
            updateSyncStatus('', 'Data synced');
        }, 2000);
    }, 1000);
}
</script>

<script>
$(document).ready(function() {
    // Animate cards on load
    $('.analytics-card, .stat-card, .user-selector, .timeline-modern, .video-progress-modern').each(function(index) {
        $(this).css('opacity', '0').css('transform', 'translateY(20px)');
        $(this).delay(index * 100).animate({
            opacity: 1
        }, 500).css('transform', 'translateY(0px)');
    });

    // Smooth scroll for internal links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Enhanced form validation
    $('form').on('submit', function(e) {
        var userSelect = $(this).find('select[name="user_id"]');
        var courseSelect = $(this).find('select[name="course_id"]');

        if (userSelect.length && !userSelect.val()) {
            e.preventDefault();
            userSelect.focus();
            showToast('Please select a user', 'warning');
            return false;
        }

        if (courseSelect.length && !courseSelect.val()) {
            e.preventDefault();
            courseSelect.focus();
            showToast('Please select a course', 'warning');
            return false;
        }
    });

    // Add loading state to buttons
    $('.btn-modern').on('click', function() {
        var $btn = $(this);
        if ($btn.attr('type') === 'submit') {
            $btn.prop('disabled', true);
            var originalText = $btn.html();
            $btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Loading...');

            setTimeout(function() {
                $btn.prop('disabled', false);
                $btn.html(originalText);
            }, 3000);
        }
    });

    // Select all videos checkbox (for modal)
    $('#selectAllVideos').on('change', function() {
        $('.video-checkbox:not(:disabled)').prop('checked', $(this).prop('checked'));
    });

    // Update select all checkbox state when individual checkboxes change
    $('.video-checkbox').on('change', function() {
        var allChecked = $('.video-checkbox:not(:disabled)').length === $('.video-checkbox:not(:disabled):checked').length;
        $('#selectAllVideos').prop('checked', allChecked);
    });

    // Toast notification function
    function showToast(message, type = 'info') {
        var toastHtml = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        if (!$('.toast-container').length) {
            $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
        }

        $('.toast-container').append(toastHtml);
        $('.toast').last().toast('show');
    }
});
</script>
